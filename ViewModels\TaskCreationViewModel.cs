using System.ComponentModel.DataAnnotations;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using TaskMaster.Models;
using TaskMaster.Services;

namespace TaskMaster.ViewModels;

/// <summary>
/// View model for task creation and editing
/// </summary>
public partial class TaskCreationViewModel : ObservableValidator
{
    private readonly ITaskService _taskService;
    private readonly ILoggingService _loggingService;
    private TaskItem? _editingTask;

    /// <summary>
    /// Initializes a new instance of TaskCreationViewModel
    /// </summary>
    /// <param name="taskService">Task service</param>
    /// <param name="loggingService">Logging service</param>
    public TaskCreationViewModel(ITaskService taskService, ILoggingService loggingService)
    {
        _taskService = taskService;
        _loggingService = loggingService;
        Title = "Create New Task";
        
        // Set default values
        TaskTitle = string.Empty;
        TaskDescription = string.Empty;
        DueDate = DateTime.Today.AddDays(1); // Default to tomorrow
        DueTime = DateTime.Now.AddHours(1).TimeOfDay; // Default to 1 hour from now
        SelectedPriority = TaskPriority.Medium;
        
        // Available priorities
        Priorities = Enum.GetValues<TaskPriority>();
    }

    #region Properties

    [ObservableProperty]
    private bool _isBusy;

    [ObservableProperty]
    private string _title = "Create New Task";

    [ObservableProperty]
    private string _errorMessage = string.Empty;

    [ObservableProperty]
    private bool _hasError;

    [ObservableProperty]
    [Required(ErrorMessage = "Title is required")]
    [StringLength(200, MinimumLength = 1, ErrorMessage = "Title must be between 1 and 200 characters")]
    private string _taskTitle = string.Empty;

    [ObservableProperty]
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    private string _taskDescription = string.Empty;

    [ObservableProperty]
    private DateTime _dueDate = DateTime.Today.AddDays(1);

    [ObservableProperty]
    private TimeSpan _dueTime = DateTime.Now.AddHours(1).TimeOfDay;

    [ObservableProperty]
    private TaskPriority _selectedPriority = TaskPriority.Medium;

    [ObservableProperty]
    private bool _isEditMode;

    [ObservableProperty]
    private string _saveButtonText = "Create Task";

    /// <summary>
    /// Available priority options
    /// </summary>
    public TaskPriority[] Priorities { get; }

    /// <summary>
    /// Combined due date and time
    /// </summary>
    public DateTime DueDateTime => DueDate.Date.Add(DueTime);

    /// <summary>
    /// Indicates if the form is valid
    /// </summary>
    public bool IsFormValid => !string.IsNullOrWhiteSpace(TaskTitle) &&
                              TaskTitle.Length <= 200 &&
                              (string.IsNullOrEmpty(TaskDescription) || TaskDescription.Length <= 1000) &&
                              DueDateTime > DateTime.Now;

    #endregion

    #region Commands

    /// <summary>
    /// Command to save the task (create or update)
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanSaveTask))]
    private async Task SaveTaskAsync()
    {
        await ExecuteAsync(async () =>
        {
            if (!ValidateForm())
            {
                throw new ValidationException("Please correct the validation errors");
            }

            if (IsEditMode && _editingTask != null)
            {
                // Update existing task
                _editingTask.Title = TaskTitle.Trim();
                _editingTask.Description = string.IsNullOrWhiteSpace(TaskDescription) ? null : TaskDescription.Trim();
                _editingTask.DueDate = DueDateTime;
                _editingTask.Priority = SelectedPriority;

                await _taskService.UpdateTaskAsync(_editingTask);
                await _loggingService.LogAsync($"Updated task: {_editingTask.Title}");
            }
            else
            {
                // Create new task
                var newTask = await _taskService.CreateTaskAsync(
                    TaskTitle.Trim(),
                    string.IsNullOrWhiteSpace(TaskDescription) ? null : TaskDescription.Trim(),
                    DueDateTime,
                    SelectedPriority);

                await _loggingService.LogAsync($"Created new task: {newTask.Title}");
            }

            // Reset form after successful save
            ResetForm();
            
        }, IsEditMode ? "Updating task..." : "Creating task...", 
           IsEditMode ? "Failed to update task" : "Failed to create task");
    }

    /// <summary>
    /// Command to cancel task creation/editing
    /// </summary>
    [RelayCommand]
    private void CancelTask()
    {
        ResetForm();
    }

    /// <summary>
    /// Command to reset the form
    /// </summary>
    [RelayCommand]
    private void ResetForm()
    {
        TaskTitle = string.Empty;
        TaskDescription = string.Empty;
        DueDate = DateTime.Today.AddDays(1);
        DueTime = DateTime.Now.AddHours(1).TimeOfDay;
        SelectedPriority = TaskPriority.Medium;
        
        _editingTask = null;
        IsEditMode = false;
        Title = "Create New Task";
        SaveButtonText = "Create Task";
        
        ClearError();
    }

    /// <summary>
    /// Command to set due date to today
    /// </summary>
    [RelayCommand]
    private void SetDueDateToday()
    {
        DueDate = DateTime.Today;
    }

    /// <summary>
    /// Command to set due date to tomorrow
    /// </summary>
    [RelayCommand]
    private void SetDueDateTomorrow()
    {
        DueDate = DateTime.Today.AddDays(1);
    }

    /// <summary>
    /// Command to set due date to next week
    /// </summary>
    [RelayCommand]
    private void SetDueDateNextWeek()
    {
        DueDate = DateTime.Today.AddDays(7);
    }

    /// <summary>
    /// Command to clear error state
    /// </summary>
    [RelayCommand]
    private void ClearErrorCommand()
    {
        ClearError();
    }

    #endregion

    #region Command Can Execute Methods

    private bool CanSaveTask() => IsFormValid && !IsBusy;

    #endregion

    #region Public Methods

    /// <summary>
    /// Sets up the view model for editing an existing task
    /// </summary>
    /// <param name="task">Task to edit</param>
    public void SetEditingTask(TaskItem task)
    {
        _editingTask = task;
        IsEditMode = true;
        Title = "Edit Task";
        SaveButtonText = "Update Task";

        TaskTitle = task.Title;
        TaskDescription = task.Description ?? string.Empty;
        DueDate = task.DueDate.Date;
        DueTime = task.DueDate.TimeOfDay;
        SelectedPriority = task.Priority;

        ClearError();
    }

    /// <summary>
    /// Gets the current task data as a TaskItem (for preview purposes)
    /// </summary>
    /// <returns>TaskItem with current form data</returns>
    public TaskItem GetCurrentTaskData()
    {
        return new TaskItem
        {
            Title = TaskTitle.Trim(),
            Description = string.IsNullOrWhiteSpace(TaskDescription) ? null : TaskDescription.Trim(),
            DueDate = DueDateTime,
            Priority = SelectedPriority,
            CreatedDate = _editingTask?.CreatedDate ?? DateTime.Now,
            IsComplete = _editingTask?.IsComplete ?? false,
            CompletedDate = _editingTask?.CompletedDate
        };
    }

    #endregion

    #region Private Methods

    /// <summary>
    /// Validates the form data
    /// </summary>
    /// <returns>True if form is valid</returns>
    private bool ValidateForm()
    {
        ClearError();

        if (string.IsNullOrWhiteSpace(TaskTitle))
        {
            SetErrorAsync("Title is required").ConfigureAwait(false);
            return false;
        }

        if (TaskTitle.Length > 200)
        {
            SetErrorAsync("Title cannot exceed 200 characters").ConfigureAwait(false);
            return false;
        }

        if (!string.IsNullOrEmpty(TaskDescription) && TaskDescription.Length > 1000)
        {
            SetErrorAsync("Description cannot exceed 1000 characters").ConfigureAwait(false);
            return false;
        }

        if (DueDateTime <= DateTime.Now)
        {
            SetErrorAsync("Due date and time must be in the future").ConfigureAwait(false);
            return false;
        }

        return true;
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Sets the busy state and optionally updates the title
    /// </summary>
    /// <param name="isBusy">Whether the view model is busy</param>
    /// <param name="busyTitle">Optional title to show while busy</param>
    protected void SetBusyState(bool isBusy, string? busyTitle = null)
    {
        IsBusy = isBusy;
        if (!string.IsNullOrEmpty(busyTitle))
        {
            Title = busyTitle;
        }
    }

    /// <summary>
    /// Sets an error message and logs it
    /// </summary>
    /// <param name="message">Error message</param>
    /// <param name="exception">Optional exception</param>
    protected async Task SetErrorAsync(string message, Exception? exception = null)
    {
        ErrorMessage = message;
        HasError = true;

        if (exception != null)
        {
            await _loggingService.LogErrorAsync(message, exception);
        }
        else
        {
            await _loggingService.LogAsync($"Error: {message}");
        }
    }

    /// <summary>
    /// Clears any error state
    /// </summary>
    protected void ClearError()
    {
        ErrorMessage = string.Empty;
        HasError = false;
    }

    /// <summary>
    /// Executes an async operation with error handling and busy state management
    /// </summary>
    /// <param name="operation">Operation to execute</param>
    /// <param name="busyMessage">Message to show while busy</param>
    /// <param name="errorMessage">Error message prefix</param>
    protected async Task ExecuteAsync(Func<Task> operation, string busyMessage = "Loading...", string errorMessage = "An error occurred")
    {
        if (IsBusy)
            return;

        try
        {
            ClearError();
            SetBusyState(true, busyMessage);

            await operation();
        }
        catch (Exception ex)
        {
            await SetErrorAsync($"{errorMessage}: {ex.Message}", ex);
        }
        finally
        {
            SetBusyState(false);
        }
    }

    #endregion

    #region Property Changed Handlers

    partial void OnTaskTitleChanged(string value)
    {
        OnPropertyChanged(nameof(IsFormValid));
        SaveTaskCommand.NotifyCanExecuteChanged();
    }

    partial void OnTaskDescriptionChanged(string value)
    {
        OnPropertyChanged(nameof(IsFormValid));
        SaveTaskCommand.NotifyCanExecuteChanged();
    }

    partial void OnDueDateChanged(DateTime value)
    {
        OnPropertyChanged(nameof(DueDateTime));
        OnPropertyChanged(nameof(IsFormValid));
        SaveTaskCommand.NotifyCanExecuteChanged();
    }

    partial void OnDueTimeChanged(TimeSpan value)
    {
        OnPropertyChanged(nameof(DueDateTime));
        OnPropertyChanged(nameof(IsFormValid));
        SaveTaskCommand.NotifyCanExecuteChanged();
    }

    #endregion
}
