<?xml version="1.0" encoding="UTF-8" ?>
<?xaml-comp compile="true" ?>
<ResourceDictionary 
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

    <!-- Light Theme Colors -->
    <Color x:Key="Primary">#512BD4</Color>
    <Color x:Key="Secondary">#DFD8F7</Color>
    <Color x:Key="Tertiary">#2B0B98</Color>
    
    <!-- Neutral Colors -->
    <Color x:Key="White">White</Color>
    <Color x:Key="Black">Black</Color>
    <Color x:Key="Gray100">#E1E1E1</Color>
    <Color x:Key="Gray200">#C8C8C8</Color>
    <Color x:Key="Gray300">#ACACAC</Color>
    <Color x:Key="Gray400">#919191</Color>
    <Color x:Key="Gray500">#6E6E6E</Color>
    <Color x:Key="Gray600">#404040</Color>
    <Color x:Key="Gray900">#212121</Color>
    <Color x:Key="Gray950">#141414</Color>
    
    <!-- Application Specific Colors -->
    <Color x:Key="PrimaryColor">#512BD4</Color>
    <Color x:Key="SecondaryColor">#DFD8F7</Color>
    <Color x:Key="TertiaryColor">#2B0B98</Color>
    <Color x:Key="BackgroundColor">#F8F9FA</Color>
    <Color x:Key="SurfaceColor">#FFFFFF</Color>
    <Color x:Key="TextColor">#212529</Color>
    <Color x:Key="SecondaryTextColor">#6C757D</Color>
    <Color x:Key="BorderColor">#DEE2E6</Color>
    
    <!-- Priority Colors -->
    <Color x:Key="HighPriorityColor">#DC3545</Color>
    <Color x:Key="MediumPriorityColor">#FD7E14</Color>
    <Color x:Key="LowPriorityColor">#28A745</Color>
    
    <!-- Status Colors -->
    <Color x:Key="SuccessColor">#28A745</Color>
    <Color x:Key="WarningColor">#FFC107</Color>
    <Color x:Key="ErrorColor">#DC3545</Color>
    <Color x:Key="InfoColor">#17A2B8</Color>
    
    <!-- Semantic Colors -->
    <Color x:Key="CompletedColor">#28A745</Color>
    <Color x:Key="OverdueColor">#DC3545</Color>
    <Color x:Key="DueSoonColor">#FFC107</Color>
    
    <!-- Light Theme -->
    <Color x:Key="LightBackground">White</Color>
    <Color x:Key="LightBackgroundAlt">#F7F7F7</Color>
    <Color x:Key="LightForeground">Black</Color>
    <Color x:Key="LightForegroundAlt">#2E2E2E</Color>
    <Color x:Key="LightPrimaryText">#1C1C1E</Color>
    <Color x:Key="LightSecondaryText">#99000000</Color>
    
    <!-- Dark Theme -->
    <Color x:Key="DarkBackground">#1C1C1E</Color>
    <Color x:Key="DarkBackgroundAlt">#2C2C2E</Color>
    <Color x:Key="DarkForeground">White</Color>
    <Color x:Key="DarkForegroundAlt">#E5E5E7</Color>
    <Color x:Key="DarkPrimaryText">White</Color>
    <Color x:Key="DarkSecondaryText">#99FFFFFF</Color>
</ResourceDictionary>
