using System.Text;

namespace TaskMaster.Services;

/// <summary>
/// Implementation of logging service for TaskMaster application
/// </summary>
public class LoggingService : ILoggingService
{
    private readonly string _logFilePath;
    private readonly object _lockObject = new();

    /// <summary>
    /// Initializes a new instance of LoggingService
    /// </summary>
    public LoggingService()
    {
        // Get the application data folder path
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        var appFolder = Path.Combine(appDataPath, "TaskMaster");
        
        // Create directory if it doesn't exist
        if (!Directory.Exists(appFolder))
        {
            Directory.CreateDirectory(appFolder);
        }
        
        _logFilePath = Path.Combine(appFolder, "TaskMaster.log");
    }

    /// <inheritdoc />
    public async Task LogAsync(string message)
    {
        await WriteLogEntryAsync("INFO", message);
    }

    /// <inheritdoc />
    public async Task LogErrorAsync(string message, Exception exception)
    {
        var fullMessage = $"{message}\nException: {exception.Message}\nStack Trace: {exception.StackTrace}";
        await WriteLogEntryAsync("ERROR", fullMessage);
    }

    /// <inheritdoc />
    public async Task LogWarningAsync(string message)
    {
        await WriteLogEntryAsync("WARNING", message);
    }

    /// <inheritdoc />
    public async Task LogDebugAsync(string message)
    {
#if DEBUG
        await WriteLogEntryAsync("DEBUG", message);
#endif
    }

    /// <inheritdoc />
    public string GetLogFilePath()
    {
        return _logFilePath;
    }

    /// <inheritdoc />
    public async Task ClearOldLogsAsync(int daysToKeep = 30)
    {
        try
        {
            if (!File.Exists(_logFilePath))
            {
                return;
            }

            var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
            var lines = await File.ReadAllLinesAsync(_logFilePath);
            var filteredLines = new List<string>();

            foreach (var line in lines)
            {
                if (TryParseLogDate(line, out var logDate) && logDate >= cutoffDate)
                {
                    filteredLines.Add(line);
                }
            }

            await File.WriteAllLinesAsync(_logFilePath, filteredLines);
        }
        catch (Exception ex)
        {
            // Can't log this error since it's in the logging service itself
            System.Diagnostics.Debug.WriteLine($"Failed to clear old logs: {ex.Message}");
        }
    }

    /// <summary>
    /// Writes a log entry to the log file
    /// </summary>
    /// <param name="level">Log level (INFO, ERROR, WARNING, DEBUG)</param>
    /// <param name="message">Message to log</param>
    private async Task WriteLogEntryAsync(string level, string message)
    {
        try
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var logEntry = $"[{timestamp}] [{level}] {message}";

            lock (_lockObject)
            {
                File.AppendAllText(_logFilePath, logEntry + Environment.NewLine, Encoding.UTF8);
            }
        }
        catch (Exception ex)
        {
            // Can't log this error since it's in the logging service itself
            System.Diagnostics.Debug.WriteLine($"Failed to write log entry: {ex.Message}");
        }
    }

    /// <summary>
    /// Tries to parse the date from a log line
    /// </summary>
    /// <param name="logLine">Log line to parse</param>
    /// <param name="logDate">Parsed date</param>
    /// <returns>True if date was parsed successfully</returns>
    private static bool TryParseLogDate(string logLine, out DateTime logDate)
    {
        logDate = DateTime.MinValue;
        
        if (string.IsNullOrEmpty(logLine) || !logLine.StartsWith("["))
        {
            return false;
        }

        var endBracket = logLine.IndexOf(']');
        if (endBracket <= 1)
        {
            return false;
        }

        var dateString = logLine.Substring(1, endBracket - 1);
        return DateTime.TryParse(dateString, out logDate);
    }
}
