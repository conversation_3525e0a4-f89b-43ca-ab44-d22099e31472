using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using TaskMaster.Services;

namespace TaskMaster.ViewModels;

/// <summary>
/// Base view model class that provides common functionality for all view models
/// </summary>
public abstract partial class BaseViewModel : ObservableObject
{
    protected readonly ITaskService _taskService;
    protected readonly ILoggingService _loggingService;

    /// <summary>
    /// Initializes a new instance of BaseViewModel
    /// </summary>
    /// <param name="taskService">Task service</param>
    /// <param name="loggingService">Logging service</param>
    protected BaseViewModel(ITaskService taskService, ILoggingService loggingService)
    {
        _taskService = taskService;
        _loggingService = loggingService;
    }

    [ObservableProperty]
    private bool _isBusy;

    [ObservableProperty]
    private string _title = string.Empty;

    [ObservableProperty]
    private string _errorMessage = string.Empty;

    [ObservableProperty]
    private bool _hasError;

    /// <summary>
    /// Sets the busy state and optionally updates the title
    /// </summary>
    /// <param name="isBusy">Whether the view model is busy</param>
    /// <param name="busyTitle">Optional title to show while busy</param>
    protected void SetBusyState(bool isBusy, string? busyTitle = null)
    {
        IsBusy = isBusy;
        if (!string.IsNullOrEmpty(busyTitle))
        {
            Title = busyTitle;
        }
    }

    /// <summary>
    /// Sets an error message and logs it
    /// </summary>
    /// <param name="message">Error message</param>
    /// <param name="exception">Optional exception</param>
    protected async Task SetErrorAsync(string message, Exception? exception = null)
    {
        ErrorMessage = message;
        HasError = true;
        
        if (exception != null)
        {
            await _loggingService.LogErrorAsync(message, exception);
        }
        else
        {
            await _loggingService.LogAsync($"Error: {message}");
        }
    }

    /// <summary>
    /// Clears any error state
    /// </summary>
    protected void ClearError()
    {
        ErrorMessage = string.Empty;
        HasError = false;
    }

    /// <summary>
    /// Executes an async operation with error handling and busy state management
    /// </summary>
    /// <param name="operation">Operation to execute</param>
    /// <param name="busyMessage">Message to show while busy</param>
    /// <param name="errorMessage">Error message prefix</param>
    protected async Task ExecuteAsync(Func<Task> operation, string busyMessage = "Loading...", string errorMessage = "An error occurred")
    {
        if (IsBusy)
            return;

        try
        {
            ClearError();
            SetBusyState(true, busyMessage);
            
            await operation();
        }
        catch (Exception ex)
        {
            await SetErrorAsync($"{errorMessage}: {ex.Message}", ex);
        }
        finally
        {
            SetBusyState(false);
        }
    }

    /// <summary>
    /// Executes an async operation that returns a result with error handling and busy state management
    /// </summary>
    /// <typeparam name="T">Return type</typeparam>
    /// <param name="operation">Operation to execute</param>
    /// <param name="busyMessage">Message to show while busy</param>
    /// <param name="errorMessage">Error message prefix</param>
    /// <returns>Result of the operation or default value if error occurred</returns>
    protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, string busyMessage = "Loading...", string errorMessage = "An error occurred")
    {
        if (IsBusy)
            return default;

        try
        {
            ClearError();
            SetBusyState(true, busyMessage);
            
            return await operation();
        }
        catch (Exception ex)
        {
            await SetErrorAsync($"{errorMessage}: {ex.Message}", ex);
            return default;
        }
        finally
        {
            SetBusyState(false);
        }
    }

    /// <summary>
    /// Command to clear error state
    /// </summary>
    [RelayCommand]
    private void ClearErrorCommand()
    {
        ClearError();
    }
}
