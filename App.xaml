<?xml version="1.0" encoding="UTF-8" ?>
<Application x:Class="TaskMaster.App"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:TaskMaster.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Value Converters -->
            <local:IsNotNullConverter x:Key="IsNotNullConverter" />
            <local:IsNullConverter x:Key="IsNullConverter" />
            <local:IsNotNullOrEmptyConverter x:Key="IsNotNullOrEmptyConverter" />
            <local:PriorityToColorConverter x:Key="PriorityToColorConverter" />
            <local:BoolToValidationMessageConverter x:Key="BoolToValidationMessageConverter" />
            <local:InverseBoolConverter x:Key="InverseBoolConverter" />
            <local:DateTimeFormatConverter x:Key="DateTimeFormatConverter" />
            <local:TimeSpanFormatConverter x:Key="TimeSpanFormatConverter" />
            <local:NumberToVisibilityConverter x:Key="NumberToVisibilityConverter" />
            
            <!-- Priority Colors -->
            <Color x:Key="HighPriorityColor">#FF4444</Color>
            <Color x:Key="MediumPriorityColor">#FFA500</Color>
            <Color x:Key="LowPriorityColor">#4CAF50</Color>
            
            <!-- Application Colors -->
            <Color x:Key="PrimaryColor">#512BD4</Color>
            <Color x:Key="SecondaryColor">#DFD8F7</Color>
            <Color x:Key="TertiaryColor">#2B0B98</Color>
            <Color x:Key="BackgroundColor">#F8F9FA</Color>
            <Color x:Key="SurfaceColor">#FFFFFF</Color>
            <Color x:Key="TextColor">#212529</Color>
            <Color x:Key="SecondaryTextColor">#6C757D</Color>
            
            <!-- Brushes -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}" />
            <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource SecondaryColor}" />
            <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}" />
            <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}" />
            <SolidColorBrush x:Key="TextBrush" Color="{StaticResource TextColor}" />
            <SolidColorBrush x:Key="SecondaryTextBrush" Color="{StaticResource SecondaryTextColor}" />
            
            <!-- Priority Brushes -->
            <SolidColorBrush x:Key="HighPriorityBrush" Color="{StaticResource HighPriorityColor}" />
            <SolidColorBrush x:Key="MediumPriorityBrush" Color="{StaticResource MediumPriorityColor}" />
            <SolidColorBrush x:Key="LowPriorityBrush" Color="{StaticResource LowPriorityColor}" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
