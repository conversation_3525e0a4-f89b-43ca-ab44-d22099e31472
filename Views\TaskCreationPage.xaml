<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="TaskMaster.Views.TaskCreationPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:TaskMaster.ViewModels"
             xmlns:models="clr-namespace:TaskMaster.Models"
             Title="{Binding Title}"
             x:DataType="vm:TaskCreationViewModel">

    <ScrollView>
        <Grid Padding="20" RowSpacing="15">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!-- Page Title -->
            <Label Grid.Row="0"
                   Text="{Binding Title}"
                   FontSize="24"
                   FontAttributes="Bold"
                   TextColor="{StaticResource TextColor}"
                   HorizontalOptions="Center"
                   Margin="0,0,0,20" />

            <!-- Task Title -->
            <StackLayout Grid.Row="1" Spacing="5">
                <Label Text="Task Title *"
                       FontSize="16"
                       FontAttributes="Bold"
                       TextColor="{StaticResource TextColor}" />
                <Border BackgroundColor="{StaticResource SurfaceColor}"
                        Stroke="{StaticResource SecondaryBrush}"
                        StrokeThickness="1">
                    <Entry Text="{Binding TaskTitle}"
                           Placeholder="Enter task title..."
                           FontSize="16"
                           BackgroundColor="Transparent"
                           BorderWidth="0"
                           Margin="10" />
                </Border>
            </StackLayout>

            <!-- Task Description -->
            <StackLayout Grid.Row="2" Spacing="5">
                <Label Text="Description (Optional)"
                       FontSize="16"
                       FontAttributes="Bold"
                       TextColor="{StaticResource TextColor}" />
                <Border BackgroundColor="{StaticResource SurfaceColor}"
                        Stroke="{StaticResource SecondaryBrush}"
                        StrokeThickness="1">
                    <Editor Text="{Binding TaskDescription}"
                            Placeholder="Enter task description..."
                            FontSize="14"
                            BackgroundColor="Transparent"
                            HeightRequest="100"
                            Margin="10" />
                </Border>
            </StackLayout>

            <!-- Due Date -->
            <StackLayout Grid.Row="3" Spacing="5">
                <Label Text="Due Date *"
                       FontSize="16"
                       FontAttributes="Bold"
                       TextColor="{StaticResource TextColor}" />
                <Grid ColumnSpacing="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    
                    <Border Grid.Column="0"
                            BackgroundColor="{StaticResource SurfaceColor}"
                            Stroke="{StaticResource SecondaryBrush}"
                            StrokeThickness="1">
                        <DatePicker Date="{Binding DueDate}"
                                    FontSize="16"
                                    BackgroundColor="Transparent"
                                    Margin="10" />
                    </Border>
                    
                    <Button Grid.Column="1"
                            Text="Today"
                            Command="{Binding SetDueDateTodayCommand}"
                            BackgroundColor="{StaticResource SecondaryColor}"
                            TextColor="{StaticResource TextColor}"
                            FontSize="12" />
                    
                    <Button Grid.Column="2"
                            Text="Tomorrow"
                            Command="{Binding SetDueDateTomorrowCommand}"
                            BackgroundColor="{StaticResource SecondaryColor}"
                            TextColor="{StaticResource TextColor}"
                            FontSize="12" />
                    
                    <Button Grid.Column="3"
                            Text="Next Week"
                            Command="{Binding SetDueDateNextWeekCommand}"
                            BackgroundColor="{StaticResource SecondaryColor}"
                            TextColor="{StaticResource TextColor}"
                            FontSize="12" />
                </Grid>
            </StackLayout>

            <!-- Due Time -->
            <StackLayout Grid.Row="4" Spacing="5">
                <Label Text="Due Time *"
                       FontSize="16"
                       FontAttributes="Bold"
                       TextColor="{StaticResource TextColor}" />
                <Border BackgroundColor="{StaticResource SurfaceColor}"
                        Stroke="{StaticResource SecondaryBrush}"
                        StrokeThickness="1">
                    <TimePicker Time="{Binding DueTime}"
                                FontSize="16"
                                BackgroundColor="Transparent"
                                Margin="10" />
                </Border>
            </StackLayout>

            <!-- Priority -->
            <StackLayout Grid.Row="5" Spacing="5">
                <Label Text="Priority *"
                       FontSize="16"
                       FontAttributes="Bold"
                       TextColor="{StaticResource TextColor}" />
                <Border BackgroundColor="{StaticResource SurfaceColor}"
                        Stroke="{StaticResource SecondaryBrush}"
                        StrokeThickness="1">
                    <Picker ItemsSource="{Binding Priorities}"
                            SelectedItem="{Binding SelectedPriority}"
                            FontSize="16"
                            BackgroundColor="Transparent"
                            Margin="10" />
                </Border>
            </StackLayout>

            <!-- Priority Visual Indicator -->
            <Grid Grid.Row="6" ColumnSpacing="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                
                <Ellipse Grid.Column="0"
                         Width="20" Height="20"
                         Fill="{Binding SelectedPriority, Converter={StaticResource PriorityToColorConverter}}"
                         VerticalOptions="Center" />
                
                <Label Grid.Column="1"
                       Text="{Binding SelectedPriority, StringFormat='Selected Priority: {0}'}"
                       FontSize="14"
                       TextColor="{StaticResource SecondaryTextColor}"
                       VerticalOptions="Center" />
            </Grid>

            <!-- Due Date Preview -->
            <Border Grid.Row="7"
                    BackgroundColor="{StaticResource SecondaryColor}"
                    Stroke="{StaticResource SecondaryBrush}"
                    StrokeThickness="1"
                    Padding="15">
                <StackLayout Spacing="5">
                    <Label Text="Task Preview"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="{StaticResource TextColor}" />
                    
                    <Label Text="{Binding DueDateTime, StringFormat='Due: {0:MMM dd, yyyy h:mm tt}'}"
                           FontSize="14"
                           TextColor="{StaticResource TextColor}" />
                    
                    <Label Text="{Binding IsFormValid, Converter={StaticResource BoolToValidationMessageConverter}}"
                           FontSize="12"
                           TextColor="{StaticResource SecondaryTextColor}" />
                </StackLayout>
            </Border>

            <!-- Spacer -->
            <BoxView Grid.Row="8" />

            <!-- Action Buttons -->
            <Grid Grid.Row="9" ColumnSpacing="15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                
                <Button Grid.Column="0"
                        Text="{Binding SaveButtonText}"
                        Command="{Binding SaveTaskCommand}"
                        BackgroundColor="{StaticResource PrimaryColor}"
                        TextColor="White"
                        FontSize="16"
                        HeightRequest="50" />
                
                <Button Grid.Column="1"
                        Text="Reset"
                        Command="{Binding ResetFormCommand}"
                        BackgroundColor="{StaticResource SecondaryColor}"
                        TextColor="{StaticResource TextColor}"
                        FontSize="16"
                        HeightRequest="50" />
                
                <Button Grid.Column="2"
                        Text="Cancel"
                        Command="{Binding CancelTaskCommand}"
                        BackgroundColor="{StaticResource HighPriorityColor}"
                        TextColor="White"
                        FontSize="16"
                        HeightRequest="50" />
            </Grid>
        </Grid>
    </ScrollView>

    <!-- Loading Indicator -->
    <Grid BackgroundColor="#80000000"
          IsVisible="{Binding IsBusy}">
        <ActivityIndicator IsRunning="{Binding IsBusy}"
                           Color="{StaticResource PrimaryColor}"
                           WidthRequest="50"
                           HeightRequest="50" />
    </Grid>

    <!-- Error Message -->
    <Border BackgroundColor="#FFEBEE"
            Stroke="{StaticResource HighPriorityColor}"
            StrokeThickness="2"
            Margin="20"
            Padding="15"
            IsVisible="{Binding HasError}"
            VerticalOptions="Top">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            
            <Label Grid.Column="0"
                   Text="{Binding ErrorMessage}"
                   TextColor="{StaticResource HighPriorityColor}"
                   FontSize="14" />
            
            <Button Grid.Column="1"
                    Text="✕"
                    Command="{Binding ClearErrorCommand}"
                    BackgroundColor="Transparent"
                    TextColor="{StaticResource HighPriorityColor}"
                    FontSize="16"
                    Padding="5" />
        </Grid>
    </Border>
</ContentPage>
