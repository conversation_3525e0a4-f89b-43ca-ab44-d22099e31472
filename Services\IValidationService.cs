using TaskMaster.Models;

namespace TaskMaster.Services;

/// <summary>
/// Interface for validation service
/// </summary>
public interface IValidationService
{
    /// <summary>
    /// Validates a task item
    /// </summary>
    /// <param name="task">Task to validate</param>
    /// <returns>Validation result</returns>
    ValidationResult ValidateTask(TaskItem task);

    /// <summary>
    /// Validates task creation data
    /// </summary>
    /// <param name="title">Task title</param>
    /// <param name="description">Task description</param>
    /// <param name="dueDate">Due date</param>
    /// <param name="priority">Priority</param>
    /// <returns>Validation result</returns>
    ValidationResult ValidateTaskCreation(string title, string? description, DateTime dueDate, TaskPriority priority);

    /// <summary>
    /// Validates a search term
    /// </summary>
    /// <param name="searchTerm">Search term to validate</param>
    /// <returns>Validation result</returns>
    ValidationResult ValidateSearchTerm(string searchTerm);

    /// <summary>
    /// Validates application settings
    /// </summary>
    /// <returns>Validation result</returns>
    ValidationResult ValidateApplicationSettings();

    /// <summary>
    /// Validates database connection
    /// </summary>
    /// <returns>Task representing the async validation result</returns>
    Task<ValidationResult> ValidateDatabaseConnectionAsync();
}

/// <summary>
/// Represents the result of a validation operation
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// Whether the validation passed
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// List of validation errors
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// List of validation warnings
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Creates a successful validation result
    /// </summary>
    /// <returns>Valid validation result</returns>
    public static ValidationResult Success()
    {
        return new ValidationResult { IsValid = true };
    }

    /// <summary>
    /// Creates a failed validation result with errors
    /// </summary>
    /// <param name="errors">Validation errors</param>
    /// <returns>Invalid validation result</returns>
    public static ValidationResult Failure(params string[] errors)
    {
        return new ValidationResult 
        { 
            IsValid = false, 
            Errors = errors.ToList() 
        };
    }

    /// <summary>
    /// Creates a validation result with warnings
    /// </summary>
    /// <param name="warnings">Validation warnings</param>
    /// <returns>Valid validation result with warnings</returns>
    public static ValidationResult WithWarnings(params string[] warnings)
    {
        return new ValidationResult 
        { 
            IsValid = true, 
            Warnings = warnings.ToList() 
        };
    }

    /// <summary>
    /// Adds an error to the validation result
    /// </summary>
    /// <param name="error">Error message</param>
    public void AddError(string error)
    {
        Errors.Add(error);
        IsValid = false;
    }

    /// <summary>
    /// Adds a warning to the validation result
    /// </summary>
    /// <param name="warning">Warning message</param>
    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }

    /// <summary>
    /// Gets all validation messages (errors and warnings)
    /// </summary>
    /// <returns>Combined error and warning messages</returns>
    public IEnumerable<string> GetAllMessages()
    {
        return Errors.Concat(Warnings);
    }

    /// <summary>
    /// Gets a formatted string of all validation messages
    /// </summary>
    /// <returns>Formatted validation messages</returns>
    public string GetFormattedMessages()
    {
        var messages = new List<string>();
        
        if (Errors.Any())
        {
            messages.Add("Errors:");
            messages.AddRange(Errors.Select(e => $"  • {e}"));
        }
        
        if (Warnings.Any())
        {
            if (messages.Any()) messages.Add("");
            messages.Add("Warnings:");
            messages.AddRange(Warnings.Select(w => $"  • {w}"));
        }
        
        return string.Join(Environment.NewLine, messages);
    }
}
