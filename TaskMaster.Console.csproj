<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<OutputType>Exe</OutputType>
		<RootNamespace>TaskMaster</RootNamespace>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<AssemblyTitle>TaskMaster Console Test</AssemblyTitle>
		<AssemblyDescription>Console test version of TaskMaster</AssemblyDescription>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8" />
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
	</ItemGroup>

	<!-- Exclude UI-related files -->
	<ItemGroup>
		<None Remove="**/*.xaml" />
		<Compile Remove="App.xaml.cs" />
		<Compile Remove="Views/*.cs" />
		<Compile Remove="ViewModels/*.cs" />
		<Compile Remove="MauiProgram.cs" />
		<Compile Remove="Converters/*.cs" />
	</ItemGroup>

</Project>
