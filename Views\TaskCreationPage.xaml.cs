using TaskMaster.ViewModels;

namespace TaskMaster.Views;

/// <summary>
/// Page for creating and editing tasks
/// </summary>
public partial class TaskCreationPage : ContentPage
{
    /// <summary>
    /// Initializes a new instance of TaskCreationPage
    /// </summary>
    /// <param name="viewModel">Task creation view model</param>
    public TaskCreationPage(TaskCreationViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }
}
