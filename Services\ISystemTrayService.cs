namespace TaskMaster.Services;

/// <summary>
/// Interface for system tray service
/// </summary>
public interface ISystemTrayService
{
    /// <summary>
    /// Event raised when the tray icon is clicked
    /// </summary>
    event EventHandler? TrayIconClicked;

    /// <summary>
    /// Event raised when the user requests to show the main window
    /// </summary>
    event EventHandler? ShowMainWindowRequested;

    /// <summary>
    /// Event raised when the user requests to exit the application
    /// </summary>
    event EventHandler? ExitApplicationRequested;

    /// <summary>
    /// Event raised when the user requests to create a new task
    /// </summary>
    event EventHandler? CreateTaskRequested;

    /// <summary>
    /// Initializes the system tray icon
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    Task InitializeAsync();

    /// <summary>
    /// Shows the system tray icon
    /// </summary>
    void ShowTrayIcon();

    /// <summary>
    /// Hides the system tray icon
    /// </summary>
    void HideTrayIcon();

    /// <summary>
    /// Updates the tray icon tooltip
    /// </summary>
    /// <param name="tooltip">New tooltip text</param>
    void UpdateTooltip(string tooltip);

    /// <summary>
    /// Shows a balloon notification from the tray icon
    /// </summary>
    /// <param name="title">Notification title</param>
    /// <param name="message">Notification message</param>
    /// <param name="timeout">Timeout in milliseconds</param>
    /// <returns>Task representing the async operation</returns>
    Task ShowBalloonNotificationAsync(string title, string message, int timeout = 5000);

    /// <summary>
    /// Updates the tray icon based on application state
    /// </summary>
    /// <param name="pendingTasks">Number of pending tasks</param>
    /// <param name="overdueTasks">Number of overdue tasks</param>
    void UpdateTrayIcon(int pendingTasks, int overdueTasks);

    /// <summary>
    /// Disposes the system tray service
    /// </summary>
    void Dispose();
}

/// <summary>
/// System tray menu item
/// </summary>
public class TrayMenuItem
{
    /// <summary>
    /// Menu item text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Whether the menu item is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Whether the menu item is a separator
    /// </summary>
    public bool IsSeparator { get; set; } = false;

    /// <summary>
    /// Action to execute when the menu item is clicked
    /// </summary>
    public Action? Action { get; set; }

    /// <summary>
    /// Creates a regular menu item
    /// </summary>
    /// <param name="text">Menu item text</param>
    /// <param name="action">Action to execute</param>
    /// <param name="enabled">Whether the item is enabled</param>
    /// <returns>Menu item</returns>
    public static TrayMenuItem Create(string text, Action action, bool enabled = true)
    {
        return new TrayMenuItem
        {
            Text = text,
            Action = action,
            Enabled = enabled
        };
    }

    /// <summary>
    /// Creates a separator menu item
    /// </summary>
    /// <returns>Separator menu item</returns>
    public static TrayMenuItem Separator()
    {
        return new TrayMenuItem
        {
            IsSeparator = true
        };
    }
}
