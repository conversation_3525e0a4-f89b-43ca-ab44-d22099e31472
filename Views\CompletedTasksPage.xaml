<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="TaskMaster.Views.CompletedTasksPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:TaskMaster.ViewModels"
             xmlns:models="clr-namespace:TaskMaster.Models"
             Title="{Binding Title}"
             x:DataType="vm:CompletedTasksViewModel">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Page Header -->
        <Grid Grid.Row="0" Padding="20,15" BackgroundColor="{StaticResource SecondaryColor}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            
            <Label Grid.Column="0"
                   Text="Completed Tasks"
                   FontSize="24"
                   FontAttributes="Bold"
                   TextColor="{StaticResource TextColor}"
                   VerticalOptions="Center" />
            
            <Label Grid.Column="1"
                   Text="{Binding TotalCompletedTasks, StringFormat='Total: {0}'}"
                   FontSize="16"
                   TextColor="{StaticResource SecondaryTextColor}"
                   VerticalOptions="Center" />
        </Grid>

        <!-- Filter and Search Toolbar -->
        <Grid Grid.Row="1" Padding="15,10" BackgroundColor="{StaticResource BackgroundColor}" ColumnSpacing="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!-- Search Box -->
            <Border Grid.Column="0" 
                    BackgroundColor="{StaticResource SurfaceColor}"
                    Stroke="{StaticResource SecondaryBrush}"
                    StrokeThickness="1">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    
                    <Entry Grid.Column="0"
                           Text="{Binding SearchText}"
                           Placeholder="Search completed tasks..."
                           BackgroundColor="Transparent"
                           BorderWidth="0" />
                    
                    <Button Grid.Column="1"
                            Text="Search"
                            Command="{Binding SearchTasksCommand}"
                            BackgroundColor="Transparent"
                            TextColor="{StaticResource PrimaryColor}" />
                    
                    <Button Grid.Column="2"
                            Text="Clear"
                            Command="{Binding ClearSearchCommand}"
                            BackgroundColor="Transparent"
                            TextColor="{StaticResource SecondaryTextColor}" />
                </Grid>
            </Border>

            <!-- Sort Picker -->
            <Picker Grid.Column="1"
                    Title="Sort by"
                    ItemsSource="{Binding SortOptions}"
                    SelectedItem="{Binding SelectedSortOption}"
                    MinimumWidthRequest="120" />

            <!-- Quick Filter Buttons -->
            <Button Grid.Column="2"
                    Text="Today"
                    Command="{Binding FilterTodayCommand}"
                    BackgroundColor="{StaticResource SecondaryColor}"
                    TextColor="{StaticResource TextColor}"
                    FontSize="12" />
            
            <Button Grid.Column="3"
                    Text="This Week"
                    Command="{Binding FilterThisWeekCommand}"
                    BackgroundColor="{StaticResource SecondaryColor}"
                    TextColor="{StaticResource TextColor}"
                    FontSize="12" />
            
            <Button Grid.Column="4"
                    Text="This Month"
                    Command="{Binding FilterThisMonthCommand}"
                    BackgroundColor="{StaticResource SecondaryColor}"
                    TextColor="{StaticResource TextColor}"
                    FontSize="12" />

            <!-- Refresh Button -->
            <Button Grid.Column="5"
                    Text="Refresh"
                    Command="{Binding RefreshTasksCommand}"
                    BackgroundColor="Transparent"
                    TextColor="{StaticResource PrimaryColor}" />
        </Grid>

        <!-- Date Range Filter -->
        <Grid Grid.Row="2" Padding="15,0,15,10" ColumnSpacing="10" IsVisible="{Binding FilterFromDate, Converter={StaticResource IsNotNullConverter}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            
            <Label Grid.Column="0"
                   Text="From:"
                   FontSize="14"
                   TextColor="{StaticResource TextColor}"
                   VerticalOptions="Center" />
            
            <DatePicker Grid.Column="1"
                        Date="{Binding FilterFromDate}"
                        FontSize="14"
                        BackgroundColor="{StaticResource SurfaceColor}" />
            
            <Label Grid.Column="2"
                   Text="To:"
                   FontSize="14"
                   TextColor="{StaticResource TextColor}"
                   VerticalOptions="Center"
                   Margin="10,0,0,0" />
            
            <DatePicker Grid.Column="3"
                        Date="{Binding FilterToDate}"
                        FontSize="14"
                        BackgroundColor="{StaticResource SurfaceColor}" />
            
            <Button Grid.Column="4"
                    Text="Clear Dates"
                    Command="{Binding ClearSearchCommand}"
                    BackgroundColor="Transparent"
                    TextColor="{StaticResource SecondaryTextColor}"
                    FontSize="12" />
        </Grid>

        <!-- Completed Tasks List -->
        <Border Grid.Row="2" 
                BackgroundColor="{StaticResource SurfaceColor}"
                Stroke="{StaticResource SecondaryBrush}"
                StrokeThickness="1"
                Margin="15,0,15,15">
            <CollectionView ItemsSource="{Binding CompletedTasks}"
                            SelectedItem="{Binding SelectedTask}"
                            SelectionMode="Single"
                            BackgroundColor="{StaticResource SurfaceColor}">
                <CollectionView.EmptyView>
                    <Grid>
                        <StackLayout HorizontalOptions="Center" VerticalOptions="Center" Spacing="10">
                            <Label Text="No completed tasks found"
                                   FontSize="18"
                                   TextColor="{StaticResource SecondaryTextColor}"
                                   HorizontalOptions="Center" />
                            <Label Text="Complete some tasks to see them here"
                                   FontSize="14"
                                   TextColor="{StaticResource SecondaryTextColor}"
                                   HorizontalOptions="Center" />
                        </StackLayout>
                    </Grid>
                </CollectionView.EmptyView>
                
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="models:TaskItem">
                        <Grid Padding="15,10" BackgroundColor="{StaticResource SurfaceColor}">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <!-- Completed Indicator -->
                            <Label Grid.Row="0" Grid.Column="0" Grid.RowSpan="3"
                                   Text="✓"
                                   FontSize="20"
                                   TextColor="{StaticResource LowPriorityColor}"
                                   VerticalOptions="Center"
                                   Margin="0,0,15,0" />

                            <!-- Task Title -->
                            <Label Grid.Row="0" Grid.Column="1"
                                   Text="{Binding Title}"
                                   FontSize="16"
                                   FontAttributes="Bold"
                                   TextColor="{StaticResource TextColor}"
                                   TextDecorations="Strikethrough" />

                            <!-- Task Description -->
                            <Label Grid.Row="1" Grid.Column="1"
                                   Text="{Binding Description}"
                                   FontSize="14"
                                   TextColor="{StaticResource SecondaryTextColor}"
                                   IsVisible="{Binding Description, Converter={StaticResource IsNotNullOrEmptyConverter}}" />

                            <!-- Completion Info -->
                            <StackLayout Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Spacing="15">
                                <Label Text="{Binding CompletedDate, StringFormat='Completed: {0:MMM dd, yyyy h:mm tt}'}"
                                       FontSize="12"
                                       TextColor="{StaticResource SecondaryTextColor}" />
                                <Label Text="{Binding PriorityText, StringFormat='Priority: {0}'}"
                                       FontSize="12"
                                       TextColor="{Binding PriorityColor}" />
                            </StackLayout>

                            <!-- Original Due Date -->
                            <Label Grid.Row="0" Grid.Column="2"
                                   Text="{Binding DueDate, StringFormat='Was due: {0:MMM dd}'}"
                                   FontSize="12"
                                   TextColor="{StaticResource SecondaryTextColor}"
                                   VerticalOptions="Center"
                                   Margin="10,0" />

                            <!-- Action Buttons -->
                            <StackLayout Grid.Row="0" Grid.Column="3" Grid.RowSpan="3" 
                                         Orientation="Horizontal" 
                                         Spacing="5"
                                         VerticalOptions="Center">
                                <Button Text="Reopen"
                                        FontSize="12"
                                        BackgroundColor="{StaticResource MediumPriorityColor}"
                                        TextColor="White"
                                        WidthRequest="60"
                                        HeightRequest="30" />
                                
                                <Button Text="Delete"
                                        FontSize="12"
                                        BackgroundColor="{StaticResource HighPriorityColor}"
                                        TextColor="White"
                                        WidthRequest="60"
                                        HeightRequest="30" />
                            </StackLayout>
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </Border>

        <!-- Selected Task Actions -->
        <Grid Grid.Row="3" 
              Padding="15,10"
              BackgroundColor="{StaticResource SecondaryColor}"
              IsVisible="{Binding SelectedTask, Converter={StaticResource IsNotNullConverter}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            
            <Label Grid.Column="0"
                   Text="{Binding SelectedTask.Title, StringFormat='Selected: {0}'}"
                   FontSize="14"
                   TextColor="{StaticResource TextColor}"
                   VerticalOptions="Center" />
            
            <Button Grid.Column="1"
                    Text="Reopen Task"
                    Command="{Binding ReopenTaskCommand}"
                    BackgroundColor="{StaticResource MediumPriorityColor}"
                    TextColor="White"
                    Margin="0,0,10,0" />
            
            <Button Grid.Column="2"
                    Text="Delete Task"
                    Command="{Binding DeleteTaskCommand}"
                    BackgroundColor="{StaticResource HighPriorityColor}"
                    TextColor="White" />
        </Grid>

        <!-- Status Bar -->
        <Grid Grid.Row="3" 
              BackgroundColor="{StaticResource BackgroundColor}"
              Padding="15,5"
              IsVisible="{Binding SelectedTask, Converter={StaticResource IsNullConverter}}">
            <Label Text="{Binding StatusMessage}"
                   FontSize="12"
                   TextColor="{StaticResource SecondaryTextColor}"
                   VerticalOptions="Center" />
        </Grid>
    </Grid>

    <!-- Loading Indicator -->
    <Grid BackgroundColor="#80000000"
          IsVisible="{Binding IsBusy}">
        <ActivityIndicator IsRunning="{Binding IsBusy}"
                           Color="{StaticResource PrimaryColor}"
                           WidthRequest="50"
                           HeightRequest="50" />
    </Grid>

    <!-- Error Message -->
    <Border BackgroundColor="#FFEBEE"
            Stroke="{StaticResource HighPriorityColor}"
            StrokeThickness="2"
            Margin="20"
            Padding="15"
            IsVisible="{Binding HasError}"
            VerticalOptions="Top">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>
            
            <Label Grid.Column="0"
                   Text="{Binding ErrorMessage}"
                   TextColor="{StaticResource HighPriorityColor}"
                   FontSize="14" />
            
            <Button Grid.Column="1"
                    Text="✕"
                    Command="{Binding ClearErrorCommand}"
                    BackgroundColor="Transparent"
                    TextColor="{StaticResource HighPriorityColor}"
                    FontSize="16"
                    Padding="5" />
        </Grid>
    </Border>
</ContentPage>
