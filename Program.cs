using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using TaskMaster.Services;
using TaskMaster.Tests;
using TaskMaster.Models;

namespace TaskMaster;

/// <summary>
/// Main program entry point for TaskMaster application
/// This version runs as a console application to test core functionality
/// </summary>
public class Program
{
    /// <summary>
    /// Main entry point
    /// </summary>
    /// <param name="args">Command line arguments</param>
    public static async Task<int> Main(string[] args)
    {
        Console.WriteLine("TaskMaster - Task Reminder Application");
        Console.WriteLine("=====================================");
        Console.WriteLine();

        try
        {
            // Check if we should run tests
            if (args.Length > 0 && args[0].ToLower() == "test")
            {
                return await TestRunner.RunTestsAsync();
            }

            // Create and configure services
            var services = ConfigureServices();
            
            // Run the application
            return await RunApplicationAsync(services);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Fatal error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            return 1;
        }
    }

    /// <summary>
    /// Configures dependency injection services
    /// </summary>
    /// <returns>Configured service provider</returns>
    private static ServiceProvider ConfigureServices()
    {
        var services = new ServiceCollection();

        // Register core services
        services.AddSingleton<ILoggingService, LoggingService>();
        services.AddSingleton<IDatabaseService, DatabaseService>();
        services.AddSingleton<IValidationService, ValidationService>();
        services.AddSingleton<ITaskService, TaskService>();
        services.AddSingleton<IErrorHandlingService, ErrorHandlingService>();
        services.AddSingleton<IHealthCheckService, HealthCheckService>();

        // Register notification service without system tray for console mode
        services.AddSingleton<INotificationService>(provider =>
        {
            var taskService = provider.GetRequiredService<ITaskService>();
            var loggingService = provider.GetRequiredService<ILoggingService>();
            return new NotificationService(taskService, loggingService, null);
        });

        return services.BuildServiceProvider();
    }

    /// <summary>
    /// Runs the main application logic
    /// </summary>
    /// <param name="serviceProvider">Service provider</param>
    /// <returns>Exit code</returns>
    private static async Task<int> RunApplicationAsync(ServiceProvider serviceProvider)
    {
        var loggingService = serviceProvider.GetRequiredService<ILoggingService>();
        var healthCheckService = serviceProvider.GetRequiredService<IHealthCheckService>();
        var taskService = serviceProvider.GetRequiredService<ITaskService>();
        var errorHandlingService = serviceProvider.GetRequiredService<IErrorHandlingService>();

        try
        {
            Console.WriteLine("Initializing TaskMaster...");
            
            // Initialize global exception handling
            errorHandlingService.InitializeGlobalExceptionHandling();
            
            // Run startup validation
            Console.WriteLine("Running startup validation...");
            var startupResult = await healthCheckService.ValidateStartupAsync();
            Console.WriteLine($"Startup validation: {startupResult.Status} - {startupResult.Message}");
            
            if (startupResult.Status == HealthStatus.Unhealthy)
            {
                Console.WriteLine("Cannot start application due to startup validation failures.");
                return 1;
            }

            // Run comprehensive health check
            Console.WriteLine("\nRunning comprehensive health check...");
            var healthResult = await healthCheckService.PerformHealthCheckAsync();
            Console.WriteLine(healthResult.GetSummary());

            // Test basic functionality
            Console.WriteLine("\nTesting basic functionality...");
            await TestBasicFunctionalityAsync(taskService);

            // Show application statistics
            Console.WriteLine("\nApplication Statistics:");
            var stats = await taskService.GetTaskStatisticsAsync();
            Console.WriteLine($"  Total Tasks: {stats.TotalTasks}");
            Console.WriteLine($"  Pending Tasks: {stats.PendingTasks}");
            Console.WriteLine($"  Completed Tasks: {stats.CompletedTasks}");
            Console.WriteLine($"  Overdue Tasks: {stats.OverdueTasks}");

            Console.WriteLine("\n✅ TaskMaster core functionality test completed successfully!");
            Console.WriteLine("\nNote: This is a console test version. The full UI application requires .NET MAUI workloads.");
            Console.WriteLine("To install MAUI workloads, run: dotnet workload install maui");

            return 0;
        }
        catch (Exception ex)
        {
            await errorHandlingService.HandleExceptionAsync(ex, "Main Application", true);
            return 1;
        }
    }

    /// <summary>
    /// Tests basic application functionality
    /// </summary>
    /// <param name="taskService">Task service</param>
    private static async Task TestBasicFunctionalityAsync(ITaskService taskService)
    {
        try
        {
            Console.WriteLine("  Creating test task...");
            
            // Create a test task
            var testTask = await taskService.CreateTaskAsync(
                "Test Task",
                "This is a test task created by the console application",
                DateTime.Now.AddDays(1),
                TaskPriority.Medium
            );
            
            Console.WriteLine($"  ✅ Created task: {testTask.Title} (ID: {testTask.Id})");

            // Get all tasks
            var allTasks = await taskService.GetAllTasksAsync();
            Console.WriteLine($"  ✅ Retrieved {allTasks.Count()} total tasks");

            // Get pending tasks
            var pendingTasks = await taskService.GetPendingTasksAsync();
            Console.WriteLine($"  ✅ Retrieved {pendingTasks.Count()} pending tasks");

            // Test task completion
            if (testTask.Id > 0)
            {
                await taskService.CompleteTaskAsync(testTask.Id);
                Console.WriteLine($"  ✅ Marked test task as complete");
            }

            // Test search
            var searchResults = await taskService.SearchTasksAsync("test");
            Console.WriteLine($"  ✅ Search found {searchResults.Count()} tasks containing 'test'");

            Console.WriteLine("  ✅ Basic functionality test completed");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Basic functionality test failed: {ex.Message}");
            throw;
        }
    }
}
