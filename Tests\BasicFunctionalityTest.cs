using TaskMaster.Models;
using TaskMaster.Services;

namespace TaskMaster.Tests;

/// <summary>
/// Basic functionality tests for TaskMaster application
/// This is a simple test class to validate core functionality
/// In a production environment, you would use a proper testing framework like xUnit or NUnit
/// </summary>
public static class BasicFunctionalityTest
{
    /// <summary>
    /// Runs all basic functionality tests
    /// </summary>
    /// <returns>Test results</returns>
    public static async Task<TestResults> RunAllTestsAsync()
    {
        var results = new TestResults();
        
        Console.WriteLine("Starting TaskMaster Basic Functionality Tests...\n");

        // Test 1: Task Model Validation
        results.AddTest("Task Model Validation", TestTaskModelValidation());

        // Test 2: Task Priority Enum
        results.AddTest("Task Priority Enum", TestTaskPriorityEnum());

        // Test 3: Task Statistics
        results.AddTest("Task Statistics", TestTaskStatistics());

        // Test 4: Validation Service
        results.AddTest("Validation Service", await TestValidationServiceAsync());

        // Test 5: Task Creation and Validation
        results.AddTest("Task Creation Validation", TestTaskCreationValidation());

        // Test 6: Date and Time Handling
        results.AddTest("Date and Time Handling", TestDateTimeHandling());

        // Test 7: Search and Filtering Logic
        results.AddTest("Search and Filtering", TestSearchAndFiltering());

        Console.WriteLine($"\nTest Results Summary:");
        Console.WriteLine($"Total Tests: {results.TotalTests}");
        Console.WriteLine($"Passed: {results.PassedTests}");
        Console.WriteLine($"Failed: {results.FailedTests}");
        Console.WriteLine($"Success Rate: {results.SuccessRate:P1}");

        if (results.FailedTests > 0)
        {
            Console.WriteLine("\nFailed Tests:");
            foreach (var failure in results.Failures)
            {
                Console.WriteLine($"  - {failure}");
            }
        }

        return results;
    }

    /// <summary>
    /// Tests task model validation
    /// </summary>
    private static bool TestTaskModelValidation()
    {
        try
        {
            // Test valid task
            var validTask = new TaskItem
            {
                Title = "Test Task",
                Description = "Test Description",
                DueDate = DateTime.Now.AddDays(1),
                Priority = TaskPriority.Medium
            };

            if (!validTask.IsValid())
            {
                Console.WriteLine("  ❌ Valid task failed validation");
                return false;
            }

            // Test invalid task (empty title)
            var invalidTask = new TaskItem
            {
                Title = "",
                DueDate = DateTime.Now.AddDays(1),
                Priority = TaskPriority.High
            };

            if (invalidTask.IsValid())
            {
                Console.WriteLine("  ❌ Invalid task passed validation");
                return false;
            }

            // Test task completion
            validTask.MarkAsComplete();
            if (!validTask.IsComplete || !validTask.CompletedDate.HasValue)
            {
                Console.WriteLine("  ❌ Task completion failed");
                return false;
            }

            Console.WriteLine("  ✅ Task model validation passed");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Task model validation failed: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Tests task priority enumeration
    /// </summary>
    private static bool TestTaskPriorityEnum()
    {
        try
        {
            // Test all priority values
            var priorities = Enum.GetValues<TaskPriority>();
            if (priorities.Length != 3)
            {
                Console.WriteLine("  ❌ Expected 3 priority levels");
                return false;
            }

            // Test priority ordering
            if ((int)TaskPriority.Low >= (int)TaskPriority.Medium ||
                (int)TaskPriority.Medium >= (int)TaskPriority.High)
            {
                Console.WriteLine("  ❌ Priority ordering is incorrect");
                return false;
            }

            Console.WriteLine("  ✅ Task priority enum passed");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Task priority enum failed: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Tests task statistics calculation
    /// </summary>
    private static bool TestTaskStatistics()
    {
        try
        {
            var tasks = new List<TaskItem>
            {
                new() { Title = "Task 1", DueDate = DateTime.Now.AddDays(1), Priority = TaskPriority.High, IsComplete = false },
                new() { Title = "Task 2", DueDate = DateTime.Now.AddDays(-1), Priority = TaskPriority.Medium, IsComplete = false },
                new() { Title = "Task 3", DueDate = DateTime.Now.AddDays(2), Priority = TaskPriority.Low, IsComplete = true, CompletedDate = DateTime.Now }
            };

            var stats = TaskStatistics.FromTasks(tasks);

            if (stats.TotalTasks != 3)
            {
                Console.WriteLine($"  ❌ Expected 3 total tasks, got {stats.TotalTasks}");
                return false;
            }

            if (stats.PendingTasks != 2)
            {
                Console.WriteLine($"  ❌ Expected 2 pending tasks, got {stats.PendingTasks}");
                return false;
            }

            if (stats.CompletedTasks != 1)
            {
                Console.WriteLine($"  ❌ Expected 1 completed task, got {stats.CompletedTasks}");
                return false;
            }

            if (stats.OverdueTasks != 1)
            {
                Console.WriteLine($"  ❌ Expected 1 overdue task, got {stats.OverdueTasks}");
                return false;
            }

            Console.WriteLine("  ✅ Task statistics passed");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Task statistics failed: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Tests validation service
    /// </summary>
    private static async Task<bool> TestValidationServiceAsync()
    {
        try
        {
            // Create mock services for testing
            var loggingService = new MockLoggingService();
            var databaseService = new MockDatabaseService();
            var validationService = new ValidationService(databaseService, loggingService);

            // Test valid task validation
            var validTask = new TaskItem
            {
                Title = "Valid Task",
                Description = "Valid Description",
                DueDate = DateTime.Now.AddDays(1),
                Priority = TaskPriority.Medium
            };

            var result = validationService.ValidateTask(validTask);
            if (!result.IsValid)
            {
                Console.WriteLine($"  ❌ Valid task failed validation: {string.Join(", ", result.Errors)}");
                return false;
            }

            // Test invalid task validation
            var invalidTask = new TaskItem
            {
                Title = "", // Invalid: empty title
                DueDate = DateTime.Now.AddDays(-1), // Invalid: past date
                Priority = TaskPriority.High
            };

            result = validationService.ValidateTask(invalidTask);
            if (result.IsValid)
            {
                Console.WriteLine("  ❌ Invalid task passed validation");
                return false;
            }

            Console.WriteLine("  ✅ Validation service passed");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Validation service failed: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Tests task creation validation
    /// </summary>
    private static bool TestTaskCreationValidation()
    {
        try
        {
            // Test valid task creation data
            var title = "New Task";
            var description = "Task description";
            var dueDate = DateTime.Now.AddDays(1);
            var priority = TaskPriority.High;

            var task = new TaskItem
            {
                Title = title,
                Description = description,
                DueDate = dueDate,
                Priority = priority
            };

            if (!task.IsValid())
            {
                Console.WriteLine("  ❌ Valid task creation data failed validation");
                return false;
            }

            // Test invalid task creation (past due date)
            var invalidTask = new TaskItem
            {
                Title = "Invalid Task",
                DueDate = DateTime.Now.AddDays(-1),
                Priority = TaskPriority.Medium
            };

            // Note: The model validation allows past dates, but business logic should prevent it
            // This test validates that the model itself works correctly

            Console.WriteLine("  ✅ Task creation validation passed");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Task creation validation failed: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Tests date and time handling
    /// </summary>
    private static bool TestDateTimeHandling()
    {
        try
        {
            var task = new TaskItem
            {
                Title = "Date Test Task",
                DueDate = DateTime.Now.AddHours(2),
                Priority = TaskPriority.Medium,
                CreatedDate = DateTime.Now
            };

            // Test time remaining calculation
            var timeRemaining = task.TimeRemaining;
            if (timeRemaining.TotalHours < 1.5 || timeRemaining.TotalHours > 2.5)
            {
                Console.WriteLine($"  ❌ Time remaining calculation incorrect: {timeRemaining.TotalHours} hours");
                return false;
            }

            // Test overdue detection
            var overdueTask = new TaskItem
            {
                Title = "Overdue Task",
                DueDate = DateTime.Now.AddHours(-1),
                Priority = TaskPriority.High
            };

            if (!overdueTask.IsOverdue)
            {
                Console.WriteLine("  ❌ Overdue detection failed");
                return false;
            }

            Console.WriteLine("  ✅ Date and time handling passed");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Date and time handling failed: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Tests search and filtering logic
    /// </summary>
    private static bool TestSearchAndFiltering()
    {
        try
        {
            var tasks = new List<TaskItem>
            {
                new() { Title = "Important Meeting", Description = "Team standup", Priority = TaskPriority.High, IsComplete = false },
                new() { Title = "Code Review", Description = "Review pull request", Priority = TaskPriority.Medium, IsComplete = false },
                new() { Title = "Documentation", Description = "Update user guide", Priority = TaskPriority.Low, IsComplete = true }
            };

            // Test filtering by completion status
            var pendingTasks = tasks.Where(t => !t.IsComplete).ToList();
            if (pendingTasks.Count != 2)
            {
                Console.WriteLine($"  ❌ Expected 2 pending tasks, got {pendingTasks.Count}");
                return false;
            }

            // Test filtering by priority
            var highPriorityTasks = tasks.Where(t => t.Priority == TaskPriority.High).ToList();
            if (highPriorityTasks.Count != 1)
            {
                Console.WriteLine($"  ❌ Expected 1 high priority task, got {highPriorityTasks.Count}");
                return false;
            }

            // Test search functionality (case-insensitive)
            var searchResults = tasks.Where(t => 
                t.Title.Contains("code", StringComparison.OrdinalIgnoreCase) ||
                (t.Description?.Contains("code", StringComparison.OrdinalIgnoreCase) ?? false)
            ).ToList();

            if (searchResults.Count != 1)
            {
                Console.WriteLine($"  ❌ Expected 1 search result for 'code', got {searchResults.Count}");
                return false;
            }

            Console.WriteLine("  ✅ Search and filtering passed");
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Search and filtering failed: {ex.Message}");
            return false;
        }
    }
}

/// <summary>
/// Test results container
/// </summary>
public class TestResults
{
    public int TotalTests { get; private set; }
    public int PassedTests { get; private set; }
    public int FailedTests => TotalTests - PassedTests;
    public double SuccessRate => TotalTests > 0 ? (double)PassedTests / TotalTests : 0;
    public List<string> Failures { get; } = new();

    public void AddTest(string testName, bool passed)
    {
        TotalTests++;
        if (passed)
        {
            PassedTests++;
        }
        else
        {
            Failures.Add(testName);
        }
    }
}

/// <summary>
/// Mock logging service for testing
/// </summary>
public class MockLoggingService : ILoggingService
{
    public Task LogAsync(string message) => Task.CompletedTask;
    public Task LogErrorAsync(string message, Exception exception) => Task.CompletedTask;
    public Task LogWarningAsync(string message) => Task.CompletedTask;
    public Task LogDebugAsync(string message) => Task.CompletedTask;
    public string GetLogFilePath() => "mock.log";
    public Task ClearOldLogsAsync(int daysToKeep = 30) => Task.CompletedTask;
}

/// <summary>
/// Mock database service for testing
/// </summary>
public class MockDatabaseService : IDatabaseService
{
    public Task InitializeAsync() => Task.CompletedTask;
    public Task<IEnumerable<TaskItem>> GetAllTasksAsync() => Task.FromResult(Enumerable.Empty<TaskItem>());
    public Task<IEnumerable<TaskItem>> GetPendingTasksAsync() => Task.FromResult(Enumerable.Empty<TaskItem>());
    public Task<IEnumerable<TaskItem>> GetCompletedTasksAsync() => Task.FromResult(Enumerable.Empty<TaskItem>());
    public Task<TaskItem?> GetTaskByIdAsync(int id) => Task.FromResult<TaskItem?>(null);
    public Task<IEnumerable<TaskItem>> GetTasksDueWithinAsync(TimeSpan timeSpan) => Task.FromResult(Enumerable.Empty<TaskItem>());
    public Task<IEnumerable<TaskItem>> GetOverdueTasksAsync() => Task.FromResult(Enumerable.Empty<TaskItem>());
    public Task<TaskItem> AddTaskAsync(TaskItem task) => Task.FromResult(task);
    public Task UpdateTaskAsync(TaskItem task) => Task.CompletedTask;
    public Task<bool> DeleteTaskAsync(int id) => Task.FromResult(true);
    public Task<bool> MarkTaskCompleteAsync(int id) => Task.FromResult(true);
    public Task<bool> MarkTaskIncompleteAsync(int id) => Task.FromResult(true);
    public Task<TaskStatistics> GetTaskStatisticsAsync() => Task.FromResult(new TaskStatistics());
    public Task<IEnumerable<TaskItem>> SearchTasksAsync(string searchTerm) => Task.FromResult(Enumerable.Empty<TaskItem>());
    public Task<IEnumerable<TaskItem>> GetTasksByPriorityAsync(TaskPriority priority) => Task.FromResult(Enumerable.Empty<TaskItem>());
    public Task<bool> BackupDatabaseAsync(string backupPath) => Task.FromResult(true);
    public Task<bool> RestoreDatabaseAsync(string backupPath) => Task.FromResult(true);
}
