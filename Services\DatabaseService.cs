using Microsoft.EntityFrameworkCore;
using TaskMaster.Models;

namespace TaskMaster.Services;

/// <summary>
/// Implementation of database service for TaskMaster application
/// </summary>
public class DatabaseService : IDatabaseService
{
    private readonly ILoggingService _loggingService;
    private TaskDbContext? _context;

    /// <summary>
    /// Initializes a new instance of DatabaseService
    /// </summary>
    /// <param name="loggingService">Logging service for error tracking</param>
    public DatabaseService(ILoggingService loggingService)
    {
        _loggingService = loggingService;
    }

    /// <summary>
    /// Gets or creates the database context
    /// </summary>
    private TaskDbContext Context
    {
        get
        {
            _context ??= new TaskDbContext();
            return _context;
        }
    }

    /// <inheritdoc />
    public async Task InitializeAsync()
    {
        try
        {
            await Context.EnsureDatabaseCreatedAsync();
            await _loggingService.LogAsync("Database initialized successfully");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to initialize database", ex);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> GetAllTasksAsync()
    {
        try
        {
            return await Context.Tasks
                .OrderBy(t => t.DueDate)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to get all tasks", ex);
            return Enumerable.Empty<TaskItem>();
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> GetPendingTasksAsync()
    {
        try
        {
            return await Context.Tasks
                .Where(t => !t.IsComplete)
                .OrderBy(t => t.DueDate)
                .ThenByDescending(t => t.Priority)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to get pending tasks", ex);
            return Enumerable.Empty<TaskItem>();
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> GetCompletedTasksAsync()
    {
        try
        {
            return await Context.Tasks
                .Where(t => t.IsComplete)
                .OrderByDescending(t => t.CompletedDate)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to get completed tasks", ex);
            return Enumerable.Empty<TaskItem>();
        }
    }

    /// <inheritdoc />
    public async Task<TaskItem?> GetTaskByIdAsync(int id)
    {
        try
        {
            return await Context.Tasks.FindAsync(id);
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync($"Failed to get task with ID {id}", ex);
            return null;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> GetTasksDueWithinAsync(TimeSpan timeSpan)
    {
        try
        {
            var cutoffTime = DateTime.Now.Add(timeSpan);
            return await Context.Tasks
                .Where(t => !t.IsComplete && t.DueDate <= cutoffTime)
                .OrderBy(t => t.DueDate)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to get tasks due within timespan", ex);
            return Enumerable.Empty<TaskItem>();
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> GetOverdueTasksAsync()
    {
        try
        {
            var now = DateTime.Now;
            return await Context.Tasks
                .Where(t => !t.IsComplete && t.DueDate < now)
                .OrderBy(t => t.DueDate)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to get overdue tasks", ex);
            return Enumerable.Empty<TaskItem>();
        }
    }

    /// <inheritdoc />
    public async Task<TaskItem> AddTaskAsync(TaskItem task)
    {
        try
        {
            if (!task.IsValid())
            {
                throw new ArgumentException("Task is not valid");
            }

            task.CreatedDate = DateTime.Now;
            Context.Tasks.Add(task);
            await Context.SaveChangesAsync();
            
            await _loggingService.LogAsync($"Added new task: {task.Title}");
            return task;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to add task", ex);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task UpdateTaskAsync(TaskItem task)
    {
        try
        {
            if (!task.IsValid())
            {
                throw new ArgumentException("Task is not valid");
            }

            Context.Tasks.Update(task);
            await Context.SaveChangesAsync();
            
            await _loggingService.LogAsync($"Updated task: {task.Title}");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to update task", ex);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<bool> DeleteTaskAsync(int id)
    {
        try
        {
            var task = await Context.Tasks.FindAsync(id);
            if (task == null)
            {
                return false;
            }

            Context.Tasks.Remove(task);
            await Context.SaveChangesAsync();

            await _loggingService.LogAsync($"Deleted task: {task.Title}");
            return true;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync($"Failed to delete task with ID {id}", ex);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> MarkTaskCompleteAsync(int id)
    {
        try
        {
            var task = await Context.Tasks.FindAsync(id);
            if (task == null)
            {
                return false;
            }

            task.MarkAsComplete();
            await Context.SaveChangesAsync();

            await _loggingService.LogAsync($"Marked task as complete: {task.Title}");
            return true;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync($"Failed to mark task complete with ID {id}", ex);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> MarkTaskIncompleteAsync(int id)
    {
        try
        {
            var task = await Context.Tasks.FindAsync(id);
            if (task == null)
            {
                return false;
            }

            task.MarkAsIncomplete();
            await Context.SaveChangesAsync();

            await _loggingService.LogAsync($"Marked task as incomplete: {task.Title}");
            return true;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync($"Failed to mark task incomplete with ID {id}", ex);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<TaskStatistics> GetTaskStatisticsAsync()
    {
        try
        {
            var allTasks = await GetAllTasksAsync();
            return TaskStatistics.FromTasks(allTasks);
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to get task statistics", ex);
            return new TaskStatistics();
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> SearchTasksAsync(string searchTerm)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetAllTasksAsync();
            }

            var lowerSearchTerm = searchTerm.ToLower();
            return await Context.Tasks
                .Where(t => t.Title.ToLower().Contains(lowerSearchTerm) ||
                           (t.Description != null && t.Description.ToLower().Contains(lowerSearchTerm)))
                .OrderBy(t => t.DueDate)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to search tasks", ex);
            return Enumerable.Empty<TaskItem>();
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> GetTasksByPriorityAsync(TaskPriority priority)
    {
        try
        {
            return await Context.Tasks
                .Where(t => t.Priority == priority)
                .OrderBy(t => t.DueDate)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync($"Failed to get tasks by priority {priority}", ex);
            return Enumerable.Empty<TaskItem>();
        }
    }

    /// <inheritdoc />
    public async Task<bool> BackupDatabaseAsync(string backupPath)
    {
        try
        {
            var sourcePath = Context.GetDatabasePath();
            if (!File.Exists(sourcePath))
            {
                await _loggingService.LogAsync("Database file does not exist for backup");
                return false;
            }

            // Ensure backup directory exists
            var backupDirectory = Path.GetDirectoryName(backupPath);
            if (!string.IsNullOrEmpty(backupDirectory) && !Directory.Exists(backupDirectory))
            {
                Directory.CreateDirectory(backupDirectory);
            }

            File.Copy(sourcePath, backupPath, true);
            await _loggingService.LogAsync($"Database backed up to: {backupPath}");
            return true;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to backup database", ex);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> RestoreDatabaseAsync(string backupPath)
    {
        try
        {
            if (!File.Exists(backupPath))
            {
                await _loggingService.LogAsync("Backup file does not exist for restore");
                return false;
            }

            var targetPath = Context.GetDatabasePath();

            // Close current context
            _context?.Dispose();
            _context = null;

            File.Copy(backupPath, targetPath, true);
            await _loggingService.LogAsync($"Database restored from: {backupPath}");
            return true;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to restore database", ex);
            return false;
        }
    }

    /// <summary>
    /// Disposes the database context
    /// </summary>
    public void Dispose()
    {
        _context?.Dispose();
    }
}
