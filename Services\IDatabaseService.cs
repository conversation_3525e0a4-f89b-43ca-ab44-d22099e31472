using TaskMaster.Models;

namespace TaskMaster.Services;

/// <summary>
/// Interface for database operations in the TaskMaster application
/// </summary>
public interface IDatabaseService
{
    /// <summary>
    /// Initializes the database and creates tables if they don't exist
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    Task InitializeAsync();

    /// <summary>
    /// Gets all tasks from the database
    /// </summary>
    /// <returns>Collection of all tasks</returns>
    Task<IEnumerable<TaskItem>> GetAllTasksAsync();

    /// <summary>
    /// Gets all pending (incomplete) tasks from the database
    /// </summary>
    /// <returns>Collection of pending tasks</returns>
    Task<IEnumerable<TaskItem>> GetPendingTasksAsync();

    /// <summary>
    /// Gets all completed tasks from the database
    /// </summary>
    /// <returns>Collection of completed tasks</returns>
    Task<IEnumerable<TaskItem>> GetCompletedTasksAsync();

    /// <summary>
    /// Gets a specific task by its ID
    /// </summary>
    /// <param name="id">Task ID</param>
    /// <returns>Task item or null if not found</returns>
    Task<TaskItem?> GetTaskByIdAsync(int id);

    /// <summary>
    /// Gets tasks that are due within the specified time span
    /// </summary>
    /// <param name="timeSpan">Time span to check for due tasks</param>
    /// <returns>Collection of tasks due within the time span</returns>
    Task<IEnumerable<TaskItem>> GetTasksDueWithinAsync(TimeSpan timeSpan);

    /// <summary>
    /// Gets overdue tasks
    /// </summary>
    /// <returns>Collection of overdue tasks</returns>
    Task<IEnumerable<TaskItem>> GetOverdueTasksAsync();

    /// <summary>
    /// Adds a new task to the database
    /// </summary>
    /// <param name="task">Task to add</param>
    /// <returns>The added task with updated ID</returns>
    Task<TaskItem> AddTaskAsync(TaskItem task);

    /// <summary>
    /// Updates an existing task in the database
    /// </summary>
    /// <param name="task">Task to update</param>
    /// <returns>Task representing the async operation</returns>
    Task UpdateTaskAsync(TaskItem task);

    /// <summary>
    /// Deletes a task from the database
    /// </summary>
    /// <param name="id">ID of the task to delete</param>
    /// <returns>True if deleted successfully, false otherwise</returns>
    Task<bool> DeleteTaskAsync(int id);

    /// <summary>
    /// Marks a task as complete
    /// </summary>
    /// <param name="id">ID of the task to mark as complete</param>
    /// <returns>True if updated successfully, false otherwise</returns>
    Task<bool> MarkTaskCompleteAsync(int id);

    /// <summary>
    /// Marks a task as incomplete
    /// </summary>
    /// <param name="id">ID of the task to mark as incomplete</param>
    /// <returns>True if updated successfully, false otherwise</returns>
    Task<bool> MarkTaskIncompleteAsync(int id);

    /// <summary>
    /// Gets task statistics
    /// </summary>
    /// <returns>Task statistics object</returns>
    Task<TaskStatistics> GetTaskStatisticsAsync();

    /// <summary>
    /// Searches tasks by title or description
    /// </summary>
    /// <param name="searchTerm">Search term</param>
    /// <returns>Collection of matching tasks</returns>
    Task<IEnumerable<TaskItem>> SearchTasksAsync(string searchTerm);

    /// <summary>
    /// Gets tasks filtered by priority
    /// </summary>
    /// <param name="priority">Priority to filter by</param>
    /// <returns>Collection of tasks with the specified priority</returns>
    Task<IEnumerable<TaskItem>> GetTasksByPriorityAsync(TaskPriority priority);

    /// <summary>
    /// Backs up the database to a specified location
    /// </summary>
    /// <param name="backupPath">Path where to save the backup</param>
    /// <returns>True if backup was successful, false otherwise</returns>
    Task<bool> BackupDatabaseAsync(string backupPath);

    /// <summary>
    /// Restores the database from a backup file
    /// </summary>
    /// <param name="backupPath">Path to the backup file</param>
    /// <returns>True if restore was successful, false otherwise</returns>
    Task<bool> RestoreDatabaseAsync(string backupPath);
}
