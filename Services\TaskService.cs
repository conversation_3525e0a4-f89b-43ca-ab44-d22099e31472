using TaskMaster.Models;

namespace TaskMaster.Services;

/// <summary>
/// Implementation of task management service for TaskMaster application
/// </summary>
public class TaskService : ITaskService
{
    private readonly IDatabaseService _databaseService;
    private readonly ILoggingService _loggingService;

    /// <summary>
    /// Initializes a new instance of TaskService
    /// </summary>
    /// <param name="databaseService">Database service</param>
    /// <param name="loggingService">Logging service</param>
    public TaskService(IDatabaseService databaseService, ILoggingService loggingService)
    {
        _databaseService = databaseService;
        _loggingService = loggingService;
    }

    /// <inheritdoc />
    public event EventHandler? TasksUpdated;

    /// <inheritdoc />
    public event EventHandler<TaskItem>? TaskAdded;

    /// <inheritdoc />
    public event EventHandler<TaskItem>? TaskCompleted;

    /// <inheritdoc />
    public event EventHandler<int>? TaskDeleted;

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> GetAllTasksAsync()
    {
        return await _databaseService.GetAllTasksAsync();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> GetPendingTasksAsync(TaskSortOption sortOption = TaskSortOption.DueDate, TaskFilterOption filterOption = TaskFilterOption.Pending)
    {
        var tasks = await _databaseService.GetPendingTasksAsync();
        
        // Apply filtering first
        var filteredTasks = ApplyFiltering(tasks, filterOption);
        
        // Then apply sorting
        var sortedTasks = ApplySorting(filteredTasks, sortOption);
        
        return sortedTasks;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> GetCompletedTasksAsync()
    {
        return await _databaseService.GetCompletedTasksAsync();
    }

    /// <inheritdoc />
    public async Task<TaskItem?> GetTaskByIdAsync(int id)
    {
        return await _databaseService.GetTaskByIdAsync(id);
    }

    /// <inheritdoc />
    public async Task<TaskItem> CreateTaskAsync(string title, string? description, DateTime dueDate, TaskPriority priority)
    {
        try
        {
            var task = new TaskItem
            {
                Title = title?.Trim() ?? throw new ArgumentException("Title cannot be null or empty", nameof(title)),
                Description = description?.Trim(),
                DueDate = dueDate,
                Priority = priority,
                CreatedDate = DateTime.Now
            };

            if (!task.IsValid())
            {
                throw new ArgumentException("Task data is not valid");
            }

            var createdTask = await _databaseService.AddTaskAsync(task);
            
            // Raise events
            TaskAdded?.Invoke(this, createdTask);
            TasksUpdated?.Invoke(this, EventArgs.Empty);
            
            await _loggingService.LogAsync($"Created new task: {createdTask.Title}");
            return createdTask;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to create task", ex);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task UpdateTaskAsync(TaskItem task)
    {
        try
        {
            if (task == null)
            {
                throw new ArgumentNullException(nameof(task));
            }

            if (!task.IsValid())
            {
                throw new ArgumentException("Task data is not valid");
            }

            await _databaseService.UpdateTaskAsync(task);
            
            // Raise event
            TasksUpdated?.Invoke(this, EventArgs.Empty);
            
            await _loggingService.LogAsync($"Updated task: {task.Title}");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to update task", ex);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<bool> DeleteTaskAsync(int id)
    {
        try
        {
            var success = await _databaseService.DeleteTaskAsync(id);
            
            if (success)
            {
                // Raise events
                TaskDeleted?.Invoke(this, id);
                TasksUpdated?.Invoke(this, EventArgs.Empty);
                
                await _loggingService.LogAsync($"Deleted task with ID: {id}");
            }
            
            return success;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync($"Failed to delete task with ID {id}", ex);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> CompleteTaskAsync(int id)
    {
        try
        {
            var task = await _databaseService.GetTaskByIdAsync(id);
            if (task == null)
            {
                return false;
            }

            var success = await _databaseService.MarkTaskCompleteAsync(id);
            
            if (success)
            {
                task.MarkAsComplete();
                
                // Raise events
                TaskCompleted?.Invoke(this, task);
                TasksUpdated?.Invoke(this, EventArgs.Empty);
                
                await _loggingService.LogAsync($"Completed task: {task.Title}");
            }
            
            return success;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync($"Failed to complete task with ID {id}", ex);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UncompleteTaskAsync(int id)
    {
        try
        {
            var success = await _databaseService.MarkTaskIncompleteAsync(id);
            
            if (success)
            {
                // Raise event
                TasksUpdated?.Invoke(this, EventArgs.Empty);
                
                await _loggingService.LogAsync($"Marked task as incomplete with ID: {id}");
            }
            
            return success;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync($"Failed to mark task incomplete with ID {id}", ex);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<TaskStatistics> GetTaskStatisticsAsync()
    {
        return await _databaseService.GetTaskStatisticsAsync();
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> SearchTasksAsync(string searchTerm)
    {
        return await _databaseService.SearchTasksAsync(searchTerm);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> GetTasksNeedingRemindersAsync(TimeSpan reminderTime)
    {
        return await _databaseService.GetTasksDueWithinAsync(reminderTime);
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TaskItem>> GetOverdueTasksAsync()
    {
        return await _databaseService.GetOverdueTasksAsync();
    }

    /// <inheritdoc />
    public IEnumerable<TaskItem> ApplySorting(IEnumerable<TaskItem> tasks, TaskSortOption sortOption)
    {
        return sortOption switch
        {
            TaskSortOption.DueDate => tasks.OrderBy(t => t.DueDate),
            TaskSortOption.Priority => tasks.OrderByDescending(t => t.Priority).ThenBy(t => t.DueDate),
            TaskSortOption.CreatedDate => tasks.OrderByDescending(t => t.CreatedDate),
            TaskSortOption.Title => tasks.OrderBy(t => t.Title),
            _ => tasks.OrderBy(t => t.DueDate)
        };
    }

    /// <inheritdoc />
    public IEnumerable<TaskItem> ApplyFiltering(IEnumerable<TaskItem> tasks, TaskFilterOption filterOption)
    {
        var now = DateTime.Now;
        var today = now.Date;
        var endOfWeek = today.AddDays(7 - (int)today.DayOfWeek);

        return filterOption switch
        {
            TaskFilterOption.All => tasks,
            TaskFilterOption.Pending => tasks.Where(t => !t.IsComplete),
            TaskFilterOption.Completed => tasks.Where(t => t.IsComplete),
            TaskFilterOption.Overdue => tasks.Where(t => !t.IsComplete && t.DueDate < now),
            TaskFilterOption.HighPriority => tasks.Where(t => !t.IsComplete && t.Priority == TaskPriority.High),
            TaskFilterOption.DueToday => tasks.Where(t => !t.IsComplete && t.DueDate.Date == today),
            TaskFilterOption.DueThisWeek => tasks.Where(t => !t.IsComplete && t.DueDate.Date <= endOfWeek),
            _ => tasks
        };
    }
}
