using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using TaskMaster.Models;
using TaskMaster.Services;

namespace TaskMaster.ViewModels;

/// <summary>
/// View model for the main window
/// </summary>
public partial class MainViewModel : BaseViewModel
{
    private readonly INotificationService _notificationService;

    /// <summary>
    /// Initializes a new instance of MainViewModel
    /// </summary>
    /// <param name="taskService">Task service</param>
    /// <param name="loggingService">Logging service</param>
    /// <param name="notificationService">Notification service</param>
    public MainViewModel(ITaskService taskService, ILoggingService loggingService, INotificationService notificationService)
        : base(taskService, loggingService)
    {
        _notificationService = notificationService;
        Title = "TaskMaster - Task Reminder Application";
        
        // Initialize collections
        PendingTasks = new ObservableCollection<TaskItem>();
        
        // Subscribe to task service events
        _taskService.TasksUpdated += OnTasksUpdated;
        _taskService.TaskAdded += OnTaskAdded;
        _taskService.TaskCompleted += OnTaskCompleted;
        _taskService.TaskDeleted += OnTaskDeleted;
        
        // Initialize with default sort and filter options
        SelectedSortOption = TaskSortOption.DueDate;
        SelectedFilterOption = TaskFilterOption.Pending;
    }

    #region Properties

    /// <summary>
    /// Collection of pending tasks
    /// </summary>
    public ObservableCollection<TaskItem> PendingTasks { get; }

    [ObservableProperty]
    private TaskItem? _selectedTask;

    [ObservableProperty]
    private TaskSortOption _selectedSortOption;

    [ObservableProperty]
    private TaskFilterOption _selectedFilterOption;

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private TaskStatistics? _taskStatistics;

    [ObservableProperty]
    private bool _showCompletedTasks;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    /// <summary>
    /// Available sort options
    /// </summary>
    public TaskSortOption[] SortOptions { get; } = Enum.GetValues<TaskSortOption>();

    /// <summary>
    /// Available filter options
    /// </summary>
    public TaskFilterOption[] FilterOptions { get; } = Enum.GetValues<TaskFilterOption>();

    #endregion

    #region Commands

    /// <summary>
    /// Command to load tasks
    /// </summary>
    [RelayCommand]
    private async Task LoadTasksAsync()
    {
        await ExecuteAsync(async () =>
        {
            var tasks = await _taskService.GetPendingTasksAsync(SelectedSortOption, SelectedFilterOption);
            
            PendingTasks.Clear();
            foreach (var task in tasks)
            {
                PendingTasks.Add(task);
            }
            
            // Update statistics
            TaskStatistics = await _taskService.GetTaskStatisticsAsync();
            
            StatusMessage = $"Loaded {PendingTasks.Count} tasks";
        }, "Loading tasks...", "Failed to load tasks");
    }

    /// <summary>
    /// Command to refresh tasks
    /// </summary>
    [RelayCommand]
    private async Task RefreshTasksAsync()
    {
        await LoadTasksAsync();
    }

    /// <summary>
    /// Command to create a new task
    /// </summary>
    [RelayCommand]
    private async Task CreateTaskAsync()
    {
        // This would typically open a dialog or navigate to a task creation page
        // For now, we'll just log the action
        await _loggingService.LogAsync("Create task command executed");
    }

    /// <summary>
    /// Command to edit the selected task
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanEditTask))]
    private async Task EditTaskAsync()
    {
        if (SelectedTask == null) return;
        
        // This would typically open a dialog or navigate to a task editing page
        await _loggingService.LogAsync($"Edit task command executed for: {SelectedTask.Title}");
    }

    /// <summary>
    /// Command to delete the selected task
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanDeleteTask))]
    private async Task DeleteTaskAsync()
    {
        if (SelectedTask == null) return;

        await ExecuteAsync(async () =>
        {
            var success = await _taskService.DeleteTaskAsync(SelectedTask.Id);
            if (success)
            {
                StatusMessage = $"Deleted task: {SelectedTask.Title}";
                SelectedTask = null;
            }
            else
            {
                throw new InvalidOperationException("Failed to delete task");
            }
        }, "Deleting task...", "Failed to delete task");
    }

    /// <summary>
    /// Command to mark the selected task as complete
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanCompleteTask))]
    private async Task CompleteTaskAsync()
    {
        if (SelectedTask == null) return;

        await ExecuteAsync(async () =>
        {
            var success = await _taskService.CompleteTaskAsync(SelectedTask.Id);
            if (success)
            {
                StatusMessage = $"Completed task: {SelectedTask.Title}";
            }
            else
            {
                throw new InvalidOperationException("Failed to complete task");
            }
        }, "Completing task...", "Failed to complete task");
    }

    /// <summary>
    /// Command to search tasks
    /// </summary>
    [RelayCommand]
    private async Task SearchTasksAsync()
    {
        await ExecuteAsync(async () =>
        {
            IEnumerable<TaskItem> tasks;
            
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                tasks = await _taskService.GetPendingTasksAsync(SelectedSortOption, SelectedFilterOption);
            }
            else
            {
                var allTasks = await _taskService.SearchTasksAsync(SearchText);
                tasks = _taskService.ApplyFiltering(allTasks, SelectedFilterOption);
                tasks = _taskService.ApplySorting(tasks, SelectedSortOption);
            }
            
            PendingTasks.Clear();
            foreach (var task in tasks)
            {
                PendingTasks.Add(task);
            }
            
            StatusMessage = $"Found {PendingTasks.Count} tasks";
        }, "Searching tasks...", "Failed to search tasks");
    }

    /// <summary>
    /// Command to clear search
    /// </summary>
    [RelayCommand]
    private async Task ClearSearchAsync()
    {
        SearchText = string.Empty;
        await LoadTasksAsync();
    }

    /// <summary>
    /// Command to show completed tasks
    /// </summary>
    [RelayCommand]
    private async Task ShowCompletedTasksAsync()
    {
        ShowCompletedTasks = !ShowCompletedTasks;
        await _loggingService.LogAsync($"Show completed tasks toggled: {ShowCompletedTasks}");
    }

    #endregion

    #region Command Can Execute Methods

    private bool CanEditTask() => SelectedTask != null && !IsBusy;
    private bool CanDeleteTask() => SelectedTask != null && !IsBusy;
    private bool CanCompleteTask() => SelectedTask != null && !SelectedTask.IsComplete && !IsBusy;

    #endregion

    #region Event Handlers

    private async void OnTasksUpdated(object? sender, EventArgs e)
    {
        await LoadTasksAsync();
    }

    private async void OnTaskAdded(object? sender, TaskItem task)
    {
        await LoadTasksAsync();
        StatusMessage = $"Added new task: {task.Title}";
    }

    private async void OnTaskCompleted(object? sender, TaskItem task)
    {
        await LoadTasksAsync();
        StatusMessage = $"Completed task: {task.Title}";
    }

    private async void OnTaskDeleted(object? sender, int taskId)
    {
        await LoadTasksAsync();
        StatusMessage = "Task deleted";
    }

    #endregion

    #region Property Changed Handlers

    partial void OnSelectedSortOptionChanged(TaskSortOption value)
    {
        _ = LoadTasksAsync();
    }

    partial void OnSelectedFilterOptionChanged(TaskFilterOption value)
    {
        _ = LoadTasksAsync();
    }

    #endregion

    /// <summary>
    /// Initializes the view model
    /// </summary>
    public async Task InitializeAsync()
    {
        await LoadTasksAsync();
    }
}
