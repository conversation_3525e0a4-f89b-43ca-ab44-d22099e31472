{"format": 1, "restore": {"G:\\vscodeapps\\taskremindergem\\TaskMaster.csproj": {}}, "projects": {"G:\\vscodeapps\\taskremindergem\\TaskMaster.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\vscodeapps\\taskremindergem\\TaskMaster.csproj", "projectName": "TaskMaster", "projectPath": "G:\\vscodeapps\\taskremindergem\\TaskMaster.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\vscodeapps\\taskremindergem\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows10.0.19041.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows10.0.19041": {"targetAlias": "net8.0-windows10.0.19041.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows10.0.19041": {"targetAlias": "net8.0-windows10.0.19041.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[8.0.91, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[8.0.91, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.19041.56, 10.0.19041.56]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win10-x64": {"#import": []}}}}}