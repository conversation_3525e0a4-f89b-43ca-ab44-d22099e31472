using TaskMaster.ViewModels;

namespace TaskMaster.Views;

/// <summary>
/// Page for viewing completed tasks
/// </summary>
public partial class CompletedTasksPage : ContentPage
{
    /// <summary>
    /// Initializes a new instance of CompletedTasksPage
    /// </summary>
    /// <param name="viewModel">Completed tasks view model</param>
    public CompletedTasksPage(CompletedTasksViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    /// <summary>
    /// Called when the page appears
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();
        
        if (BindingContext is CompletedTasksViewModel viewModel)
        {
            await viewModel.InitializeAsync();
        }
    }
}
