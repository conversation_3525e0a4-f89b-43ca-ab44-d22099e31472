using System.Timers;
using TaskMaster.Models;

namespace TaskMaster.Services;

/// <summary>
/// Implementation of notification service for TaskMaster application
/// </summary>
public class NotificationService : INotificationService, IDisposable
{
    private readonly ITaskService _taskService;
    private readonly ILoggingService _loggingService;
    private readonly ISystemTrayService? _systemTrayService;
    private System.Timers.Timer? _reminderTimer;
    private TimeSpan _reminderTime = TimeSpan.FromMinutes(15); // Default: 15 minutes before due
    private TimeSpan _checkInterval = TimeSpan.FromMinutes(1); // Default: Check every minute
    private readonly HashSet<int> _notifiedTasks = new(); // Track which tasks we've already notified about
    private bool _disposed = false;

    /// <summary>
    /// Initializes a new instance of NotificationService
    /// </summary>
    /// <param name="taskService">Task service</param>
    /// <param name="loggingService">Logging service</param>
    /// <param name="systemTrayService">System tray service (optional)</param>
    public NotificationService(ITaskService taskService, ILoggingService loggingService, ISystemTrayService? systemTrayService = null)
    {
        _taskService = taskService;
        _loggingService = loggingService;
        _systemTrayService = systemTrayService;
    }

    /// <inheritdoc />
    public event EventHandler? TrayIconClicked;

    /// <inheritdoc />
    public event EventHandler? ShowMainWindowRequested;

    /// <inheritdoc />
    public event EventHandler? ExitApplicationRequested;

    /// <inheritdoc />
    public async Task InitializeAsync()
    {
        try
        {
            // Initialize system tray if available
            if (_systemTrayService != null)
            {
                await _systemTrayService.InitializeAsync();
                _systemTrayService.ShowTrayIcon();
            }

            await _loggingService.LogAsync("Notification service initialized");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to initialize notification service", ex);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task ShowTaskReminderAsync(TaskItem task)
    {
        try
        {
            var title = "Task Reminder";
            var message = $"Task '{task.Title}' is due {GetDueTimeText(task.DueDate)}";
            
            await ShowNotificationAsync(title, message);
            await _loggingService.LogAsync($"Showed reminder for task: {task.Title}");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to show task reminder", ex);
        }
    }

    /// <inheritdoc />
    public async Task ShowNotificationAsync(string title, string message, bool isError = false)
    {
        try
        {
            // For now, we'll use a simple approach. In a full implementation,
            // you would use Windows Toast notifications or similar
            await ShowBalloonNotificationAsync(title, message);
            
            var logLevel = isError ? "ERROR" : "INFO";
            await _loggingService.LogAsync($"[{logLevel}] Notification: {title} - {message}");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to show notification", ex);
        }
    }

    /// <inheritdoc />
    public async Task ShowBalloonNotificationAsync(string title, string message, int timeout = 5000)
    {
        try
        {
            // Try to use system tray service first
            if (_systemTrayService != null)
            {
                await _systemTrayService.ShowBalloonNotificationAsync(title, message, timeout);
            }
            else
            {
                // Fallback to debug output
                System.Diagnostics.Debug.WriteLine($"NOTIFICATION: {title} - {message}");
            }

            await _loggingService.LogAsync($"Balloon notification: {title} - {message}");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to show balloon notification", ex);
        }
    }

    /// <inheritdoc />
    public async Task UpdateTrayTooltipAsync(string tooltip)
    {
        try
        {
            // Placeholder for updating system tray tooltip
            await _loggingService.LogDebugAsync($"Updated tray tooltip: {tooltip}");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to update tray tooltip", ex);
        }
    }

    /// <inheritdoc />
    public void StartReminderService()
    {
        try
        {
            StopReminderService(); // Stop any existing timer
            
            _reminderTimer = new System.Timers.Timer(_checkInterval.TotalMilliseconds);
            _reminderTimer.Elapsed += OnReminderTimerElapsed;
            _reminderTimer.AutoReset = true;
            _reminderTimer.Start();
            
            _loggingService.LogAsync("Reminder service started").ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _loggingService.LogErrorAsync("Failed to start reminder service", ex).ConfigureAwait(false);
        }
    }

    /// <inheritdoc />
    public void StopReminderService()
    {
        try
        {
            if (_reminderTimer != null)
            {
                _reminderTimer.Stop();
                _reminderTimer.Dispose();
                _reminderTimer = null;
                
                _loggingService.LogAsync("Reminder service stopped").ConfigureAwait(false);
            }
        }
        catch (Exception ex)
        {
            _loggingService.LogErrorAsync("Failed to stop reminder service", ex).ConfigureAwait(false);
        }
    }

    /// <inheritdoc />
    public void SetReminderInterval(TimeSpan interval)
    {
        _checkInterval = interval;
        
        if (_reminderTimer != null)
        {
            _reminderTimer.Interval = interval.TotalMilliseconds;
        }
    }

    /// <inheritdoc />
    public void SetReminderTime(TimeSpan reminderTime)
    {
        _reminderTime = reminderTime;
    }

    /// <inheritdoc />
    public void SetTrayIconVisible(bool visible)
    {
        // Placeholder for system tray icon visibility
        _loggingService.LogDebugAsync($"Tray icon visibility set to: {visible}").ConfigureAwait(false);
    }

    /// <inheritdoc />
    public async Task CheckAndNotifyTaskRemindersAsync()
    {
        try
        {
            var tasksNeedingReminders = await _taskService.GetTasksNeedingRemindersAsync(_reminderTime);
            
            foreach (var task in tasksNeedingReminders)
            {
                // Only notify once per task
                if (!_notifiedTasks.Contains(task.Id))
                {
                    await ShowTaskReminderAsync(task);
                    _notifiedTasks.Add(task.Id);
                }
            }
            
            // Clean up notifications for completed or deleted tasks
            var allTasks = await _taskService.GetAllTasksAsync();
            var existingTaskIds = allTasks.Select(t => t.Id).ToHashSet();
            _notifiedTasks.RemoveWhere(id => !existingTaskIds.Contains(id));
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to check and notify task reminders", ex);
        }
    }

    /// <inheritdoc />
    public (TimeSpan ReminderTime, TimeSpan CheckInterval) GetReminderSettings()
    {
        return (_reminderTime, _checkInterval);
    }

    /// <summary>
    /// Event handler for reminder timer elapsed
    /// </summary>
    private async void OnReminderTimerElapsed(object? sender, ElapsedEventArgs e)
    {
        await CheckAndNotifyTaskRemindersAsync();
    }

    /// <summary>
    /// Gets a user-friendly text representation of when a task is due
    /// </summary>
    /// <param name="dueDate">Due date</param>
    /// <returns>User-friendly due time text</returns>
    private static string GetDueTimeText(DateTime dueDate)
    {
        var timeRemaining = dueDate - DateTime.Now;
        
        if (timeRemaining.TotalMinutes < 0)
            return "now (overdue)";
        
        if (timeRemaining.TotalMinutes < 60)
            return $"in {(int)timeRemaining.TotalMinutes} minute(s)";
        
        if (timeRemaining.TotalHours < 24)
            return $"in {(int)timeRemaining.TotalHours} hour(s)";
        
        return $"on {dueDate:MMM dd, yyyy 'at' h:mm tt}";
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (!_disposed)
        {
            StopReminderService();
            _disposed = true;
        }
    }
}
