using TaskMaster.ViewModels;

namespace TaskMaster.Views;

/// <summary>
/// Main window for the TaskMaster application
/// </summary>
public partial class MainWindow : Window
{
    /// <summary>
    /// Initializes a new instance of MainWindow
    /// </summary>
    /// <param name="viewModel">Main view model</param>
    public MainWindow(MainViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    /// <summary>
    /// Called when the window is loaded
    /// </summary>
    protected override async void OnAppearing()
    {
        base.OnAppearing();
        
        if (BindingContext is MainViewModel viewModel)
        {
            await viewModel.InitializeAsync();
        }
    }
}
