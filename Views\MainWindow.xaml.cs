using TaskMaster.ViewModels;

namespace TaskMaster.Views;

/// <summary>
/// Main window for the TaskMaster application
/// </summary>
public partial class MainWindow : Window
{
    /// <summary>
    /// Initializes a new instance of MainWindow
    /// </summary>
    public MainWindow()
    {
        InitializeComponent();
    }

    /// <summary>
    /// Called when the window is created
    /// </summary>
    protected override void OnCreated()
    {
        base.OnCreated();

        if (BindingContext is MainViewModel viewModel)
        {
            _ = Task.Run(async () => await viewModel.InitializeAsync());
        }
    }
}
