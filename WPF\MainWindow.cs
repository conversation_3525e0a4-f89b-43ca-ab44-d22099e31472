using Microsoft.Extensions.DependencyInjection;
using System.Windows;
using System.Windows.Controls;
using TaskMaster.Models;
using TaskMaster.Services;

namespace TaskMaster.WPF;

/// <summary>
/// Simple WPF main window for TaskMaster application
/// </summary>
public class MainWindow : Window
{
    private readonly ServiceProvider _serviceProvider;
    private readonly ITaskService _taskService;
    private readonly ILoggingService _loggingService;
    private readonly IHealthCheckService _healthCheckService;
    
    private ListBox _taskListBox;
    private TextBox _titleTextBox;
    private TextBox _descriptionTextBox;
    private DatePicker _dueDatePicker;
    private ComboBox _priorityComboBox;
    private TextBlock _statusTextBlock;

    /// <summary>
    /// Initializes a new instance of MainWindow
    /// </summary>
    /// <param name="serviceProvider">Service provider</param>
    public MainWindow(ServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _taskService = serviceProvider.GetRequiredService<ITaskService>();
        _loggingService = serviceProvider.GetRequiredService<ILoggingService>();
        _healthCheckService = serviceProvider.GetRequiredService<IHealthCheckService>();
        
        InitializeWindow();
        InitializeAsync();
    }

    /// <summary>
    /// Initializes the window
    /// </summary>
    private void InitializeWindow()
    {
        Title = "TaskMaster - Task Reminder Application";
        Width = 800;
        Height = 600;
        WindowStartupLocation = WindowStartupLocation.CenterScreen;

        // Create main grid
        var mainGrid = new Grid();
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        
        mainGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
        mainGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(300) });

        // Task list
        _taskListBox = new ListBox
        {
            Margin = new Thickness(10),
            DisplayMemberPath = "DisplayText"
        };
        Grid.SetRow(_taskListBox, 0);
        Grid.SetColumn(_taskListBox, 0);
        mainGrid.Children.Add(_taskListBox);

        // Task creation panel
        var taskPanel = CreateTaskCreationPanel();
        Grid.SetRow(taskPanel, 0);
        Grid.SetColumn(taskPanel, 1);
        mainGrid.Children.Add(taskPanel);

        // Buttons
        var buttonPanel = CreateButtonPanel();
        Grid.SetRow(buttonPanel, 1);
        Grid.SetColumnSpan(buttonPanel, 2);
        mainGrid.Children.Add(buttonPanel);

        // Status bar
        _statusTextBlock = new TextBlock
        {
            Text = "Ready",
            Margin = new Thickness(10, 5),
            Background = System.Windows.Media.Brushes.LightGray,
            Padding = new Thickness(5)
        };
        Grid.SetRow(_statusTextBlock, 2);
        Grid.SetColumnSpan(_statusTextBlock, 2);
        mainGrid.Children.Add(_statusTextBlock);

        Content = mainGrid;
    }

    /// <summary>
    /// Creates the task creation panel
    /// </summary>
    /// <returns>Task creation panel</returns>
    private StackPanel CreateTaskCreationPanel()
    {
        var panel = new StackPanel
        {
            Margin = new Thickness(10),
            Orientation = Orientation.Vertical
        };

        // Title
        panel.Children.Add(new TextBlock { Text = "Create New Task", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 10) });

        // Task title
        panel.Children.Add(new TextBlock { Text = "Title:" });
        _titleTextBox = new TextBox { Margin = new Thickness(0, 0, 0, 10) };
        panel.Children.Add(_titleTextBox);

        // Description
        panel.Children.Add(new TextBlock { Text = "Description:" });
        _descriptionTextBox = new TextBox 
        { 
            Margin = new Thickness(0, 0, 0, 10),
            Height = 60,
            TextWrapping = TextWrapping.Wrap,
            AcceptsReturn = true
        };
        panel.Children.Add(_descriptionTextBox);

        // Due date
        panel.Children.Add(new TextBlock { Text = "Due Date:" });
        _dueDatePicker = new DatePicker 
        { 
            Margin = new Thickness(0, 0, 0, 10),
            SelectedDate = DateTime.Today.AddDays(1)
        };
        panel.Children.Add(_dueDatePicker);

        // Priority
        panel.Children.Add(new TextBlock { Text = "Priority:" });
        _priorityComboBox = new ComboBox 
        { 
            Margin = new Thickness(0, 0, 0, 10),
            ItemsSource = Enum.GetValues<TaskPriority>(),
            SelectedItem = TaskPriority.Medium
        };
        panel.Children.Add(_priorityComboBox);

        return panel;
    }

    /// <summary>
    /// Creates the button panel
    /// </summary>
    /// <returns>Button panel</returns>
    private StackPanel CreateButtonPanel()
    {
        var panel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(10)
        };

        var createButton = new Button
        {
            Content = "Create Task",
            Margin = new Thickness(5),
            Padding = new Thickness(10, 5),
            MinWidth = 100
        };
        createButton.Click += CreateTaskButton_Click;
        panel.Children.Add(createButton);

        var completeButton = new Button
        {
            Content = "Complete Selected",
            Margin = new Thickness(5),
            Padding = new Thickness(10, 5),
            MinWidth = 100
        };
        completeButton.Click += CompleteTaskButton_Click;
        panel.Children.Add(completeButton);

        var refreshButton = new Button
        {
            Content = "Refresh",
            Margin = new Thickness(5),
            Padding = new Thickness(10, 5),
            MinWidth = 100
        };
        refreshButton.Click += RefreshButton_Click;
        panel.Children.Add(refreshButton);

        var healthCheckButton = new Button
        {
            Content = "Health Check",
            Margin = new Thickness(5),
            Padding = new Thickness(10, 5),
            MinWidth = 100
        };
        healthCheckButton.Click += HealthCheckButton_Click;
        panel.Children.Add(healthCheckButton);

        return panel;
    }

    /// <summary>
    /// Initializes the application asynchronously
    /// </summary>
    private async void InitializeAsync()
    {
        try
        {
            UpdateStatus("Initializing application...");
            
            // Run health check
            var healthResult = await _healthCheckService.ValidateStartupAsync();
            if (healthResult.Status == HealthStatus.Unhealthy)
            {
                MessageBox.Show($"Startup validation failed: {healthResult.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // Load tasks
            await LoadTasksAsync();
            
            UpdateStatus("Application initialized successfully");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Initialization failed: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            await _loggingService.LogErrorAsync("Application initialization failed", ex);
        }
    }

    /// <summary>
    /// Loads tasks from the database
    /// </summary>
    private async Task LoadTasksAsync()
    {
        try
        {
            var tasks = await _taskService.GetPendingTasksAsync();
            _taskListBox.ItemsSource = tasks.Select(t => new TaskDisplayItem(t)).ToList();
            
            var stats = await _taskService.GetTaskStatisticsAsync();
            UpdateStatus($"Loaded {stats.PendingTasks} pending tasks, {stats.CompletedTasks} completed");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to load tasks: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            await _loggingService.LogErrorAsync("Failed to load tasks", ex);
        }
    }

    /// <summary>
    /// Updates the status message
    /// </summary>
    /// <param name="message">Status message</param>
    private void UpdateStatus(string message)
    {
        _statusTextBlock.Text = $"{DateTime.Now:HH:mm:ss} - {message}";
    }

    /// <summary>
    /// Handles create task button click
    /// </summary>
    private async void CreateTaskButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(_titleTextBox.Text))
            {
                MessageBox.Show("Please enter a task title.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (!_dueDatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("Please select a due date.", "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var dueDate = _dueDatePicker.SelectedDate.Value.Date.Add(TimeSpan.FromHours(23).Add(TimeSpan.FromMinutes(59)));
            var priority = (TaskPriority)_priorityComboBox.SelectedItem;

            var task = await _taskService.CreateTaskAsync(
                _titleTextBox.Text.Trim(),
                string.IsNullOrWhiteSpace(_descriptionTextBox.Text) ? null : _descriptionTextBox.Text.Trim(),
                dueDate,
                priority);

            // Clear form
            _titleTextBox.Clear();
            _descriptionTextBox.Clear();
            _dueDatePicker.SelectedDate = DateTime.Today.AddDays(1);
            _priorityComboBox.SelectedItem = TaskPriority.Medium;

            // Reload tasks
            await LoadTasksAsync();
            
            UpdateStatus($"Created task: {task.Title}");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to create task: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            await _loggingService.LogErrorAsync("Failed to create task", ex);
        }
    }

    /// <summary>
    /// Handles complete task button click
    /// </summary>
    private async void CompleteTaskButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (_taskListBox.SelectedItem is TaskDisplayItem selectedItem)
            {
                await _taskService.CompleteTaskAsync(selectedItem.Task.Id);
                await LoadTasksAsync();
                UpdateStatus($"Completed task: {selectedItem.Task.Title}");
            }
            else
            {
                MessageBox.Show("Please select a task to complete.", "No Selection", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Failed to complete task: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            await _loggingService.LogErrorAsync("Failed to complete task", ex);
        }
    }

    /// <summary>
    /// Handles refresh button click
    /// </summary>
    private async void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        await LoadTasksAsync();
    }

    /// <summary>
    /// Handles health check button click
    /// </summary>
    private async void HealthCheckButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            UpdateStatus("Running health check...");
            var healthResult = await _healthCheckService.PerformHealthCheckAsync();
            
            var message = $"Health Check Results:\n\n{healthResult.GetSummary()}";
            MessageBox.Show(message, "Health Check", MessageBoxButton.OK, 
                healthResult.Status == HealthStatus.Healthy ? MessageBoxImage.Information : MessageBoxImage.Warning);
            
            UpdateStatus($"Health check completed: {healthResult.Status}");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Health check failed: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            await _loggingService.LogErrorAsync("Health check failed", ex);
        }
    }
}

/// <summary>
/// Display wrapper for tasks in the list box
/// </summary>
public class TaskDisplayItem
{
    public TaskItem Task { get; }
    public string DisplayText { get; }

    public TaskDisplayItem(TaskItem task)
    {
        Task = task;
        var priorityIcon = task.Priority switch
        {
            TaskPriority.High => "🔴",
            TaskPriority.Medium => "🟡",
            TaskPriority.Low => "🟢",
            _ => "⚪"
        };
        
        var timeInfo = task.IsOverdue ? " (OVERDUE)" : 
                      task.TimeRemaining.TotalHours < 24 ? $" (Due in {task.TimeRemaining.TotalHours:F0}h)" : 
                      $" (Due {task.DueDate:MMM dd})";
        
        DisplayText = $"{priorityIcon} {task.Title}{timeInfo}";
    }
}
