using Microsoft.UI.Xaml;

namespace TaskMaster.WinUI;

public class Program
{
    [STAThread]
    public static void Main(string[] args)
    {
        WinRT.ComWrappersSupport.InitializeComWrappers();
        Application.Start((p) => {
            var context = new ActivationContext(p);
            new App();
        });
    }
}

public class ActivationContext
{
    public object Args { get; }

    public ActivationContext(object args)
    {
        Args = args;
    }
}
