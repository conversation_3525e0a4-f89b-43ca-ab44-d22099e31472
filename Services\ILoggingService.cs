namespace TaskMaster.Services;

/// <summary>
/// Interface for logging service in the TaskMaster application
/// </summary>
public interface ILoggingService
{
    /// <summary>
    /// Logs an informational message
    /// </summary>
    /// <param name="message">Message to log</param>
    /// <returns>Task representing the async operation</returns>
    Task LogAsync(string message);

    /// <summary>
    /// Logs an error message with exception details
    /// </summary>
    /// <param name="message">Error message</param>
    /// <param name="exception">Exception that occurred</param>
    /// <returns>Task representing the async operation</returns>
    Task LogErrorAsync(string message, Exception exception);

    /// <summary>
    /// Logs a warning message
    /// </summary>
    /// <param name="message">Warning message</param>
    /// <returns>Task representing the async operation</returns>
    Task LogWarningAsync(string message);

    /// <summary>
    /// Logs a debug message (only in debug builds)
    /// </summary>
    /// <param name="message">Debug message</param>
    /// <returns>Task representing the async operation</returns>
    Task LogDebugAsync(string message);

    /// <summary>
    /// Gets the log file path
    /// </summary>
    /// <returns>Full path to the log file</returns>
    string GetLogFilePath();

    /// <summary>
    /// Clears old log entries (keeps only recent entries)
    /// </summary>
    /// <param name="daysToKeep">Number of days of logs to keep</param>
    /// <returns>Task representing the async operation</returns>
    Task ClearOldLogsAsync(int daysToKeep = 30);
}
