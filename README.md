# TaskMaster - Task Reminder Application

A production-ready Windows desktop application for managing personal task reminders, built with .NET MAUI and following MVVM architecture patterns.

## Features

### Core Functionality
- **Task Management**: Create, edit, delete, and complete tasks
- **Task Properties**: Title, description, due date/time, and priority levels (Low, Medium, High)
- **Task Organization**: Sort by due date, priority, creation date, or title
- **Task Filtering**: Filter by status, priority, due date, or custom criteria
- **Search**: Full-text search across task titles and descriptions

### User Interface
- **Modern UI**: Clean, intuitive interface with Material Design principles
- **Task List View**: Comprehensive list with priority indicators and status
- **Task Details Panel**: Detailed view of selected tasks with action buttons
- **Task Creation/Editing**: User-friendly forms with validation
- **Completed Tasks View**: Separate view for completed tasks with history

### System Integration
- **System Tray**: Runs in system tray for background operation
- **Notifications**: System notifications for task reminders
- **Data Persistence**: SQLite database for reliable local storage
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Logging**: Detailed logging for debugging and monitoring

### Advanced Features
- **Reminder System**: Configurable reminders before task due dates
- **Task Statistics**: Overview of pending, completed, and overdue tasks
- **Data Validation**: Input validation with helpful error messages
- **Health Monitoring**: Application health checks and diagnostics
- **Backup/Restore**: Database backup and restore functionality

## Technical Architecture

### Technology Stack
- **.NET 8**: Latest .NET framework for modern development
- **.NET MAUI**: Cross-platform UI framework (Windows-focused)
- **Entity Framework Core**: Object-relational mapping with SQLite
- **CommunityToolkit.Mvvm**: MVVM framework for clean architecture
- **SQLite**: Lightweight, embedded database

### Project Structure
```
TaskMaster/
├── Models/                 # Data models and entities
│   ├── TaskItem.cs        # Main task entity
│   ├── TaskPriority.cs    # Priority enumeration
│   └── TaskStatistics.cs  # Statistics and filtering models
├── Services/              # Business logic and data services
│   ├── DatabaseService.cs # Database operations
│   ├── TaskService.cs     # Task management logic
│   ├── NotificationService.cs # System notifications
│   ├── LoggingService.cs  # Application logging
│   ├── ValidationService.cs # Data validation
│   ├── ErrorHandlingService.cs # Error management
│   └── HealthCheckService.cs # Application health monitoring
├── ViewModels/            # MVVM view models
│   ├── BaseViewModel.cs   # Base class with common functionality
│   ├── MainViewModel.cs   # Main window view model
│   ├── TaskCreationViewModel.cs # Task creation/editing
│   └── CompletedTasksViewModel.cs # Completed tasks view
├── Views/                 # UI views and pages
│   ├── MainWindow.xaml    # Main application window
│   ├── TaskCreationPage.xaml # Task creation/editing page
│   └── CompletedTasksPage.xaml # Completed tasks page
├── Converters/            # Value converters for data binding
├── Resources/             # Application resources
│   ├── Styles/           # XAML styles and themes
│   ├── AppIcon/          # Application icons
│   └── Splash/           # Splash screen assets
└── Platform-specific files
```

### Design Patterns
- **MVVM (Model-View-ViewModel)**: Clean separation of concerns
- **Dependency Injection**: Service registration and resolution
- **Repository Pattern**: Data access abstraction
- **Observer Pattern**: Event-driven updates
- **Command Pattern**: User action handling

## Prerequisites

### Development Environment
- **Visual Studio 2022** (17.8 or later) with .NET MAUI workload
- **.NET 8 SDK** (8.0.100 or later)
- **Windows 10/11** for development and deployment

### Required Workloads
- .NET Multi-platform App UI development
- .NET desktop development (optional, for WPF fallback)

## Building and Running

### Clone the Repository
```bash
git clone <repository-url>
cd TaskMaster
```

### Restore Dependencies
```bash
dotnet restore
```

### Build the Application
```bash
# Debug build
dotnet build

# Release build
dotnet build -c Release
```

### Run the Application
```bash
# Run in development mode
dotnet run

# Or use Visual Studio
# Open TaskMaster.sln and press F5
```

### Package for Distribution
```bash
# Create a self-contained deployment
dotnet publish -c Release -r win-x64 --self-contained true

# Create a framework-dependent deployment
dotnet publish -c Release -r win-x64 --self-contained false
```

## Configuration

### Database Location
The application stores its SQLite database in:
```
%APPDATA%\TaskMaster\TaskMaster.db
```

### Log Files
Application logs are stored in:
```
%APPDATA%\TaskMaster\TaskMaster.log
```

### Application Settings
- **Reminder Time**: Default 15 minutes before due date
- **Check Interval**: Default 1 minute for reminder checks
- **Log Retention**: Default 30 days

## Usage Guide

### Creating Tasks
1. Click "New Task" button or use File → New Task
2. Fill in task details:
   - **Title**: Required, up to 200 characters
   - **Description**: Optional, up to 1000 characters
   - **Due Date**: Required, must be in the future
   - **Priority**: Low, Medium, or High
3. Click "Create Task" to save

### Managing Tasks
- **Complete**: Click the checkmark button or use the Complete Task button
- **Edit**: Select a task and click "Edit Task"
- **Delete**: Select a task and click "Delete Task"
- **Search**: Use the search box to find tasks by title or description
- **Filter**: Use the filter dropdown to show specific task types
- **Sort**: Use the sort dropdown to organize tasks

### System Tray
- The application runs in the system tray when minimized
- Right-click the tray icon for quick actions
- Notifications appear for upcoming task due dates

## Troubleshooting

### Common Issues

#### Database Connection Errors
- Ensure the application has write permissions to %APPDATA%
- Check available disk space (minimum 100 MB recommended)
- Try running as administrator if permission issues persist

#### Performance Issues
- Check system resources (memory usage should be under 500 MB)
- Clear old log files if disk space is low
- Restart the application if memory usage is high

#### Notification Issues
- Ensure Windows notifications are enabled
- Check Windows notification settings for the application
- Verify the application is running in the system tray

### Health Check
The application includes built-in health monitoring:
- Database connectivity
- File system permissions
- System resource usage
- Service availability

### Logging
Detailed logs are available for troubleshooting:
- Application startup and shutdown
- Database operations
- Error conditions
- User actions

## Development

### Adding New Features
1. Create models in the `Models` folder
2. Implement business logic in `Services`
3. Create view models in `ViewModels`
4. Design UI in `Views`
5. Add validation and error handling
6. Update tests and documentation

### Testing
- Unit tests for services and view models
- Integration tests for database operations
- UI tests for user workflows
- Performance tests for large datasets

### Contributing
1. Follow the existing code style and patterns
2. Add comprehensive error handling
3. Include logging for debugging
4. Update documentation
5. Test on multiple Windows versions

## License

This project is provided as a complete example implementation. Modify and use as needed for your requirements.

## Support

For issues and questions:
1. Check the application logs in %APPDATA%\TaskMaster\
2. Run the built-in health check
3. Review this documentation
4. Check system requirements and permissions
