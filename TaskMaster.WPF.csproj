<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0-windows</TargetFramework>
		<OutputType>WinExe</OutputType>
		<RootNamespace>TaskMaster</RootNamespace>
		<UseWPF>true</UseWPF>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<AssemblyTitle>TaskMaster WPF</AssemblyTitle>
		<AssemblyDescription>Task Reminder Application - WPF Version</AssemblyDescription>
		<AssemblyVersion>*******</AssemblyVersion>
		<FileVersion>*******</FileVersion>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8" />
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
	</ItemGroup>

	<!-- Exclude MAUI-specific files -->
	<ItemGroup>
		<ApplicationDefinition Remove="App.xaml" />
		<Page Remove="**/*.xaml" />
		<None Include="**/*.xaml" />
		<Compile Remove="App.xaml.cs" />
		<Compile Remove="Views/*.cs" />
		<Compile Remove="ViewModels/*.cs" />
		<Compile Remove="MauiProgram.cs" />
		<Compile Remove="Program.cs" />
		<Compile Remove="Tests/**/*.cs" />
		<Compile Remove="Converters/*.cs" />
		<Compile Remove="Platforms/**/*.cs" />
		<Compile Remove="Resources/**/*.cs" />
	</ItemGroup>

</Project>
