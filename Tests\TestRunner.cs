using TaskMaster.Services;

namespace TaskMaster.Tests;

/// <summary>
/// Simple test runner for TaskMaster application
/// This can be used to validate the application before deployment
/// </summary>
public static class TestRunner
{
    /// <summary>
    /// Main entry point for running tests
    /// </summary>
    public static async Task<int> RunTestsAsync()
    {
        Console.WriteLine("=".PadRight(60, '='));
        Console.WriteLine("TaskMaster Application Test Suite");
        Console.WriteLine("=".PadRight(60, '='));
        Console.WriteLine();

        try
        {
            // Run basic functionality tests
            var basicTests = await BasicFunctionalityTest.RunAllTestsAsync();
            
            Console.WriteLine();
            Console.WriteLine("-".PadRight(60, '-'));
            
            // Run application health check if services are available
            await RunHealthCheckAsync();
            
            Console.WriteLine();
            Console.WriteLine("-".PadRight(60, '-'));
            
            // Run integration tests
            await RunIntegrationTestsAsync();
            
            Console.WriteLine();
            Console.WriteLine("=".PadRight(60, '='));
            Console.WriteLine("Test Suite Completed");
            Console.WriteLine("=".PadRight(60, '='));
            
            // Return exit code based on test results
            return basicTests.FailedTests > 0 ? 1 : 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Test suite failed with exception: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            return 1;
        }
    }

    /// <summary>
    /// Runs application health check
    /// </summary>
    private static async Task RunHealthCheckAsync()
    {
        Console.WriteLine("Running Application Health Check...\n");
        
        try
        {
            // Create minimal services for health check
            var loggingService = new TestLoggingService();
            var databaseService = new DatabaseService(loggingService);
            var validationService = new ValidationService(databaseService, loggingService);
            var healthCheckService = new HealthCheckService(databaseService, loggingService, validationService);
            
            // Run startup validation
            var startupResult = await healthCheckService.ValidateStartupAsync();
            Console.WriteLine($"  Startup Validation: {GetStatusIcon(startupResult.Status)} {startupResult.Message}");
            
            // Run file system check
            var fileSystemResult = await healthCheckService.CheckFileSystemHealthAsync();
            Console.WriteLine($"  File System: {GetStatusIcon(fileSystemResult.Status)} {fileSystemResult.Message}");
            
            // Run system resources check
            var resourcesResult = await healthCheckService.CheckSystemResourcesAsync();
            Console.WriteLine($"  System Resources: {GetStatusIcon(resourcesResult.Status)} {resourcesResult.Message}");
            
            Console.WriteLine($"\n  Overall Health Check: {GetOverallStatus(startupResult, fileSystemResult, resourcesResult)}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Health check failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Runs integration tests
    /// </summary>
    private static async Task RunIntegrationTestsAsync()
    {
        Console.WriteLine("Running Integration Tests...\n");
        
        try
        {
            // Test 1: Database Operations
            await TestDatabaseOperationsAsync();
            
            // Test 2: Service Integration
            await TestServiceIntegrationAsync();
            
            // Test 3: Error Handling
            await TestErrorHandlingAsync();
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Integration tests failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Tests database operations
    /// </summary>
    private static async Task TestDatabaseOperationsAsync()
    {
        try
        {
            var loggingService = new TestLoggingService();
            var databaseService = new DatabaseService(loggingService);
            
            // Test database initialization
            await databaseService.InitializeAsync();
            Console.WriteLine("  ✅ Database initialization");
            
            // Test basic operations
            var stats = await databaseService.GetTaskStatisticsAsync();
            Console.WriteLine($"  ✅ Database query (found {stats.TotalTasks} tasks)");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Database operations failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Tests service integration
    /// </summary>
    private static async Task TestServiceIntegrationAsync()
    {
        try
        {
            var loggingService = new TestLoggingService();
            var databaseService = new DatabaseService(loggingService);
            var taskService = new TaskService(databaseService, loggingService);
            
            // Test service initialization
            await databaseService.InitializeAsync();
            
            // Test task service operations
            var tasks = await taskService.GetAllTasksAsync();
            Console.WriteLine($"  ✅ Task service integration (loaded {tasks.Count()} tasks)");
            
            // Test statistics
            var stats = await taskService.GetTaskStatisticsAsync();
            Console.WriteLine($"  ✅ Statistics service integration");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Service integration failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Tests error handling
    /// </summary>
    private static async Task TestErrorHandlingAsync()
    {
        try
        {
            var loggingService = new TestLoggingService();
            var notificationService = new TestNotificationService();
            var errorHandlingService = new ErrorHandlingService(loggingService, notificationService);
            
            // Test error handling with a sample exception
            var testException = new InvalidOperationException("Test exception for error handling");
            await errorHandlingService.HandleExceptionAsync(testException, "Test Context", false);
            
            Console.WriteLine("  ✅ Error handling service");
            
            // Test user-friendly error messages
            var userMessage = errorHandlingService.GetUserFriendlyErrorMessage(testException);
            if (!string.IsNullOrEmpty(userMessage))
            {
                Console.WriteLine("  ✅ User-friendly error messages");
            }
            else
            {
                Console.WriteLine("  ❌ User-friendly error messages failed");
            }
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ Error handling test failed: {ex.Message}");
        }
    }

    /// <summary>
    /// Gets status icon for display
    /// </summary>
    private static string GetStatusIcon(HealthStatus status)
    {
        return status switch
        {
            HealthStatus.Healthy => "✅",
            HealthStatus.Degraded => "⚠️",
            HealthStatus.Unhealthy => "❌",
            _ => "❓"
        };
    }

    /// <summary>
    /// Gets overall status from multiple health check results
    /// </summary>
    private static string GetOverallStatus(params HealthCheckResult[] results)
    {
        var worstStatus = results.Max(r => r.Status);
        return GetStatusIcon(worstStatus) + " " + worstStatus.ToString();
    }
}

/// <summary>
/// Test logging service that outputs to console
/// </summary>
public class TestLoggingService : ILoggingService
{
    public Task LogAsync(string message)
    {
        Console.WriteLine($"[INFO] {message}");
        return Task.CompletedTask;
    }

    public Task LogErrorAsync(string message, Exception exception)
    {
        Console.WriteLine($"[ERROR] {message}: {exception.Message}");
        return Task.CompletedTask;
    }

    public Task LogWarningAsync(string message)
    {
        Console.WriteLine($"[WARN] {message}");
        return Task.CompletedTask;
    }

    public Task LogDebugAsync(string message)
    {
        Console.WriteLine($"[DEBUG] {message}");
        return Task.CompletedTask;
    }

    public string GetLogFilePath() => "test.log";

    public Task ClearOldLogsAsync(int daysToKeep = 30) => Task.CompletedTask;
}

/// <summary>
/// Test notification service
/// </summary>
public class TestNotificationService : INotificationService
{
    public event EventHandler? TrayIconClicked;
    public event EventHandler? ShowMainWindowRequested;
    public event EventHandler? ExitApplicationRequested;

    public Task InitializeAsync() => Task.CompletedTask;
    public Task ShowTaskReminderAsync(Models.TaskItem task) => Task.CompletedTask;
    public Task ShowNotificationAsync(string title, string message, bool isError = false) => Task.CompletedTask;
    public Task ShowBalloonNotificationAsync(string title, string message, int timeout = 5000) => Task.CompletedTask;
    public Task UpdateTrayTooltipAsync(string tooltip) => Task.CompletedTask;
    public void StartReminderService() { }
    public void StopReminderService() { }
    public void SetReminderInterval(TimeSpan interval) { }
    public void SetReminderTime(TimeSpan reminderTime) { }
    public void SetTrayIconVisible(bool visible) { }
    public Task CheckAndNotifyTaskRemindersAsync() => Task.CompletedTask;
    public (TimeSpan ReminderTime, TimeSpan CheckInterval) GetReminderSettings() => (TimeSpan.FromMinutes(15), TimeSpan.FromMinutes(1));
    public void Dispose() { }
}
