using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Net;

namespace TaskMaster.Services;

/// <summary>
/// Implementation of global error handling service
/// </summary>
public class ErrorHandlingService : IErrorHandlingService
{
    private readonly ILoggingService _loggingService;
    private readonly INotificationService _notificationService;

    /// <summary>
    /// Initializes a new instance of ErrorHandlingService
    /// </summary>
    /// <param name="loggingService">Logging service</param>
    /// <param name="notificationService">Notification service</param>
    public ErrorHandlingService(ILoggingService loggingService, INotificationService notificationService)
    {
        _loggingService = loggingService;
        _notificationService = notificationService;
    }

    /// <inheritdoc />
    public event EventHandler<UnhandledExceptionEventArgs>? UnhandledException;

    /// <inheritdoc />
    public async Task HandleExceptionAsync(Exception exception, string context, bool showToUser = true)
    {
        try
        {
            // Log the exception
            await _loggingService.LogErrorAsync($"Exception in {context}", exception);

            // Determine if this is a critical exception
            var isCritical = IsCriticalException(exception);

            if (showToUser)
            {
                var userMessage = GetUserFriendlyErrorMessage(exception);
                var title = isCritical ? "Critical Error" : "Error";
                
                await ShowErrorToUserAsync(title, userMessage, !isCritical);
            }

            // Raise the unhandled exception event
            var eventArgs = new UnhandledExceptionEventArgs(exception, context);
            UnhandledException?.Invoke(this, eventArgs);

            // If critical and not handled by event subscribers, we might need to restart
            if (isCritical && !eventArgs.Handled)
            {
                await _loggingService.LogAsync("Critical exception occurred, application may need to restart");
                await _notificationService.ShowNotificationAsync(
                    "Critical Error", 
                    "A critical error occurred. Please restart the application.", 
                    true);
            }
        }
        catch (Exception ex)
        {
            // Last resort logging - write to debug output
            System.Diagnostics.Debug.WriteLine($"Error in error handling: {ex.Message}");
        }
    }

    /// <inheritdoc />
    public async Task HandleValidationErrorAsync(string message, string context)
    {
        try
        {
            await _loggingService.LogWarningAsync($"Validation error in {context}: {message}");
            await ShowErrorToUserAsync("Validation Error", message, true);
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Error handling validation error", ex);
        }
    }

    /// <inheritdoc />
    public async Task ShowErrorToUserAsync(string title, string message, bool isRecoverable = true)
    {
        try
        {
            // Show notification to user
            await _notificationService.ShowNotificationAsync(title, message, !isRecoverable);
            
            // Log that we showed the error to the user
            await _loggingService.LogAsync($"Showed error to user: {title} - {message}");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Failed to show error to user", ex);
        }
    }

    /// <inheritdoc />
    public string GetUserFriendlyErrorMessage(Exception exception)
    {
        return exception switch
        {
            ValidationException validationEx => $"Validation Error: {validationEx.Message}",
            UnauthorizedAccessException => "You don't have permission to perform this action.",
            FileNotFoundException => "A required file could not be found. Please check your installation.",
            DirectoryNotFoundException => "A required directory could not be found. Please check your installation.",
            IOException ioEx => $"File operation failed: {ioEx.Message}",
            InvalidOperationException invalidOpEx => $"Operation failed: {invalidOpEx.Message}",
            ArgumentException argEx => $"Invalid input: {argEx.Message}",
            NotSupportedException => "This operation is not supported on your system.",
            TimeoutException => "The operation timed out. Please try again.",
            HttpRequestException httpEx => $"Network error: {httpEx.Message}",
            DataException dataEx => $"Database error: {dataEx.Message}",
            OutOfMemoryException => "The application is running low on memory. Please close other applications and try again.",
            StackOverflowException => "A critical error occurred. Please restart the application.",
            AccessViolationException => "A critical system error occurred. Please restart the application.",
            _ => "An unexpected error occurred. Please try again or contact support if the problem persists."
        };
    }

    /// <inheritdoc />
    public bool IsCriticalException(Exception exception)
    {
        return exception switch
        {
            OutOfMemoryException => true,
            StackOverflowException => true,
            AccessViolationException => true,
            AppDomainUnloadedException => true,
            BadImageFormatException => true,
            CannotUnloadAppDomainException => true,
            ExecutionEngineException => true,
            InvalidProgramException => true,
            ThreadAbortException => true,
            _ => false
        };
    }

    /// <inheritdoc />
    public void InitializeGlobalExceptionHandling()
    {
        try
        {
            // Handle unhandled exceptions in the current AppDomain
            AppDomain.CurrentDomain.UnhandledException += OnAppDomainUnhandledException;
            
            // Handle unhandled exceptions in tasks
            TaskScheduler.UnobservedTaskException += OnTaskSchedulerUnobservedTaskException;

            _loggingService.LogAsync("Global exception handling initialized").ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to initialize global exception handling: {ex.Message}");
        }
    }

    /// <summary>
    /// Handles unhandled exceptions in the AppDomain
    /// </summary>
    private async void OnAppDomainUnhandledException(object sender, System.UnhandledExceptionEventArgs e)
    {
        if (e.ExceptionObject is Exception exception)
        {
            await HandleExceptionAsync(exception, "AppDomain", true);
        }
    }

    /// <summary>
    /// Handles unobserved task exceptions
    /// </summary>
    private async void OnTaskSchedulerUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
    {
        await HandleExceptionAsync(e.Exception, "TaskScheduler", true);
        e.SetObserved(); // Mark as observed to prevent app termination
    }
}
