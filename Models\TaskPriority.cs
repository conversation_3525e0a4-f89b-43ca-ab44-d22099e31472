namespace TaskMaster.Models;

/// <summary>
/// Enumeration representing the priority levels for tasks
/// </summary>
public enum TaskPriority
{
    /// <summary>
    /// Low priority task - can be completed when time permits
    /// </summary>
    Low = 1,
    
    /// <summary>
    /// Medium priority task - should be completed in reasonable time
    /// </summary>
    Medium = 2,
    
    /// <summary>
    /// High priority task - requires immediate attention
    /// </summary>
    High = 3
}
