<?xml version="1.0" encoding="utf-8" ?>
<Window x:Class="TaskMaster.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
        xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
        xmlns:local="clr-namespace:TaskMaster.Views"
        xmlns:vm="clr-namespace:TaskMaster.ViewModels"
        xmlns:models="clr-namespace:TaskMaster.Models"
        Title="{Binding Title}"
        x:DataType="vm:MainViewModel">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Menu Bar -->
        <MenuBar Grid.Row="0" BackgroundColor="{StaticResource SurfaceColor}">
            <MenuBarItem Text="File">
                <MenuFlyoutItem Text="New Task" Command="{Binding CreateTaskCommand}" />
                <MenuFlyoutSeparator />
                <MenuFlyoutItem Text="Exit" />
            </MenuBarItem>
            <MenuBarItem Text="View">
                <MenuFlyoutItem Text="Refresh" Command="{Binding RefreshTasksCommand}" />
                <MenuFlyoutItem Text="Show Completed Tasks" Command="{Binding ShowCompletedTasksCommand}" />
            </MenuBarItem>
            <MenuBarItem Text="Help">
                <MenuFlyoutItem Text="About TaskMaster" />
            </MenuBarItem>
        </MenuBar>

        <!-- Toolbar -->
        <Grid Grid.Row="1" Padding="10" BackgroundColor="{StaticResource BackgroundColor}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!-- New Task Button -->
            <Button Grid.Column="0" 
                    Text="New Task" 
                    Command="{Binding CreateTaskCommand}"
                    BackgroundColor="{StaticResource PrimaryColor}"
                    TextColor="White"
                    Margin="0,0,10,0" />

            <!-- Search Box -->
            <Border Grid.Column="1" 
                    BackgroundColor="{StaticResource SurfaceColor}"
                    Stroke="{StaticResource SecondaryBrush}"
                    StrokeThickness="1"
                    Margin="0,0,10,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    
                    <Entry Grid.Column="0"
                           Text="{Binding SearchText}"
                           Placeholder="Search tasks..."
                           BackgroundColor="Transparent"
                           BorderWidth="0" />
                    
                    <Button Grid.Column="1"
                            Text="Search"
                            Command="{Binding SearchTasksCommand}"
                            BackgroundColor="Transparent"
                            TextColor="{StaticResource PrimaryColor}" />
                    
                    <Button Grid.Column="2"
                            Text="Clear"
                            Command="{Binding ClearSearchCommand}"
                            BackgroundColor="Transparent"
                            TextColor="{StaticResource SecondaryTextColor}" />
                </Grid>
            </Border>

            <!-- Sort Picker -->
            <Picker Grid.Column="2"
                    Title="Sort by"
                    ItemsSource="{Binding SortOptions}"
                    SelectedItem="{Binding SelectedSortOption}"
                    Margin="0,0,10,0"
                    MinimumWidthRequest="120" />

            <!-- Filter Picker -->
            <Picker Grid.Column="3"
                    Title="Filter"
                    ItemsSource="{Binding FilterOptions}"
                    SelectedItem="{Binding SelectedFilterOption}"
                    Margin="0,0,10,0"
                    MinimumWidthRequest="120" />

            <!-- Refresh Button -->
            <Button Grid.Column="4"
                    Text="Refresh"
                    Command="{Binding RefreshTasksCommand}"
                    BackgroundColor="Transparent"
                    TextColor="{StaticResource PrimaryColor}" />
        </Grid>

        <!-- Main Content -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!-- Task List -->
            <Border Grid.Column="0" 
                    BackgroundColor="{StaticResource SurfaceColor}"
                    Stroke="{StaticResource SecondaryBrush}"
                    StrokeThickness="1"
                    Margin="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!-- List Header -->
                    <Grid Grid.Row="0" 
                          BackgroundColor="{StaticResource SecondaryColor}"
                          Padding="10">
                        <Label Text="Pending Tasks" 
                               FontSize="18" 
                               FontAttributes="Bold"
                               TextColor="{StaticResource TextColor}" />
                    </Grid>

                    <!-- Task List View -->
                    <CollectionView Grid.Row="1"
                                    ItemsSource="{Binding PendingTasks}"
                                    SelectedItem="{Binding SelectedTask}"
                                    SelectionMode="Single"
                                    BackgroundColor="{StaticResource SurfaceColor}">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:TaskItem">
                                <Grid Padding="15,10" BackgroundColor="{StaticResource SurfaceColor}">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <!-- Priority Indicator -->
                                    <Ellipse Grid.Row="0" Grid.Column="0" Grid.RowSpan="3"
                                             Width="12" Height="12"
                                             Fill="{Binding PriorityColor}"
                                             VerticalOptions="Center"
                                             Margin="0,0,10,0" />

                                    <!-- Task Title -->
                                    <Label Grid.Row="0" Grid.Column="1"
                                           Text="{Binding Title}"
                                           FontSize="16"
                                           FontAttributes="Bold"
                                           TextColor="{StaticResource TextColor}" />

                                    <!-- Task Description -->
                                    <Label Grid.Row="1" Grid.Column="1"
                                           Text="{Binding Description}"
                                           FontSize="14"
                                           TextColor="{StaticResource SecondaryTextColor}"
                                           IsVisible="{Binding Description, Converter={StaticResource IsNotNullOrEmptyConverter}}" />

                                    <!-- Due Date and Priority -->
                                    <StackLayout Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Spacing="15">
                                        <Label Text="{Binding DueDate, StringFormat='Due: {0:MMM dd, yyyy h:mm tt}'}"
                                               FontSize="12"
                                               TextColor="{StaticResource SecondaryTextColor}" />
                                        <Label Text="{Binding PriorityText, StringFormat='Priority: {0}'}"
                                               FontSize="12"
                                               TextColor="{Binding PriorityColor}" />
                                    </StackLayout>

                                    <!-- Time Remaining -->
                                    <Label Grid.Row="0" Grid.Column="2"
                                           Text="{Binding TimeRemainingText}"
                                           FontSize="12"
                                           TextColor="{StaticResource SecondaryTextColor}"
                                           VerticalOptions="Center"
                                           Margin="10,0" />

                                    <!-- Complete Button -->
                                    <Button Grid.Row="0" Grid.Column="3" Grid.RowSpan="3"
                                            Text="✓"
                                            FontSize="16"
                                            BackgroundColor="{StaticResource LowPriorityColor}"
                                            TextColor="White"
                                            WidthRequest="40"
                                            HeightRequest="40"
                                            CornerRadius="20"
                                            VerticalOptions="Center" />
                                </Grid>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </Grid>
            </Border>

            <!-- Task Details Panel -->
            <Border Grid.Column="1" 
                    BackgroundColor="{StaticResource SurfaceColor}"
                    Stroke="{StaticResource SecondaryBrush}"
                    StrokeThickness="1"
                    Margin="0,10,10,10"
                    WidthRequest="300"
                    IsVisible="{Binding SelectedTask, Converter={StaticResource IsNotNullConverter}}">
                <Grid Padding="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!-- Details Header -->
                    <Label Grid.Row="0"
                           Text="Task Details"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="{StaticResource TextColor}"
                           Margin="0,0,0,15" />

                    <!-- Task Details -->
                    <ScrollView Grid.Row="1">
                        <StackLayout Spacing="10" IsVisible="{Binding SelectedTask, Converter={StaticResource IsNotNullConverter}}">
                            <Label Text="{Binding SelectedTask.Title}"
                                   FontSize="16"
                                   FontAttributes="Bold"
                                   TextColor="{StaticResource TextColor}" />
                            
                            <Label Text="{Binding SelectedTask.Description}"
                                   FontSize="14"
                                   TextColor="{StaticResource SecondaryTextColor}"
                                   IsVisible="{Binding SelectedTask.Description, Converter={StaticResource IsNotNullOrEmptyConverter}}" />
                            
                            <StackLayout Spacing="5">
                                <Label Text="{Binding SelectedTask.DueDate, StringFormat='Due: {0:MMM dd, yyyy h:mm tt}'}"
                                       FontSize="14"
                                       TextColor="{StaticResource TextColor}" />
                                
                                <Label Text="{Binding SelectedTask.PriorityText, StringFormat='Priority: {0}'}"
                                       FontSize="14"
                                       TextColor="{Binding SelectedTask.PriorityColor}" />
                                
                                <Label Text="{Binding SelectedTask.CreatedDate, StringFormat='Created: {0:MMM dd, yyyy h:mm tt}'}"
                                       FontSize="12"
                                       TextColor="{StaticResource SecondaryTextColor}" />
                                
                                <Label Text="{Binding SelectedTask.TimeRemainingText}"
                                       FontSize="14"
                                       TextColor="{StaticResource SecondaryTextColor}" />
                            </StackLayout>
                        </StackLayout>
                    </ScrollView>

                    <!-- Action Buttons -->
                    <StackLayout Grid.Row="2" Spacing="10" Margin="0,15,0,0">
                        <Button Text="Complete Task"
                                Command="{Binding CompleteTaskCommand}"
                                BackgroundColor="{StaticResource LowPriorityColor}"
                                TextColor="White" />
                        
                        <Button Text="Edit Task"
                                Command="{Binding EditTaskCommand}"
                                BackgroundColor="{StaticResource PrimaryColor}"
                                TextColor="White" />
                        
                        <Button Text="Delete Task"
                                Command="{Binding DeleteTaskCommand}"
                                BackgroundColor="{StaticResource HighPriorityColor}"
                                TextColor="White" />
                    </StackLayout>
                </Grid>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <Grid Grid.Row="3" 
              BackgroundColor="{StaticResource SecondaryColor}"
              Padding="10,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <Label Grid.Column="0"
                   Text="{Binding StatusMessage}"
                   FontSize="12"
                   TextColor="{StaticResource SecondaryTextColor}"
                   VerticalOptions="Center" />

            <StackLayout Grid.Column="1" Orientation="Horizontal" Spacing="15">
                <Label Text="{Binding TaskStatistics.PendingTasks, StringFormat='Pending: {0}'}"
                       FontSize="12"
                       TextColor="{StaticResource TextColor}" />
                
                <Label Text="{Binding TaskStatistics.CompletedTasks, StringFormat='Completed: {0}'}"
                       FontSize="12"
                       TextColor="{StaticResource TextColor}" />
                
                <Label Text="{Binding TaskStatistics.OverdueTasks, StringFormat='Overdue: {0}'}"
                       FontSize="12"
                       TextColor="{StaticResource HighPriorityColor}" />
            </StackLayout>
        </Grid>

        <!-- Loading Indicator -->
        <Grid Grid.RowSpan="4" 
              BackgroundColor="#80000000"
              IsVisible="{Binding IsBusy}">
            <ActivityIndicator IsRunning="{Binding IsBusy}"
                               Color="{StaticResource PrimaryColor}"
                               WidthRequest="50"
                               HeightRequest="50" />
        </Grid>

        <!-- Error Message -->
        <Border Grid.RowSpan="4"
                BackgroundColor="#FFEBEE"
                Stroke="{StaticResource HighPriorityColor}"
                StrokeThickness="2"
                Margin="20"
                Padding="15"
                IsVisible="{Binding HasError}"
                VerticalOptions="Top">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                
                <Label Grid.Column="0"
                       Text="{Binding ErrorMessage}"
                       TextColor="{StaticResource HighPriorityColor}"
                       FontSize="14" />
                
                <Button Grid.Column="1"
                        Text="✕"
                        Command="{Binding ClearErrorCommand}"
                        BackgroundColor="Transparent"
                        TextColor="{StaticResource HighPriorityColor}"
                        FontSize="16"
                        Padding="5" />
            </Grid>
        </Border>
    </Grid>
</Window>
