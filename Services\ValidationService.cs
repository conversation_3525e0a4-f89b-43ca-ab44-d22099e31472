using TaskMaster.Models;

namespace TaskMaster.Services;

/// <summary>
/// Implementation of validation service
/// </summary>
public class ValidationService : IValidationService
{
    private readonly IDatabaseService _databaseService;
    private readonly ILoggingService _loggingService;

    /// <summary>
    /// Initializes a new instance of ValidationService
    /// </summary>
    /// <param name="databaseService">Database service</param>
    /// <param name="loggingService">Logging service</param>
    public ValidationService(IDatabaseService databaseService, ILoggingService loggingService)
    {
        _databaseService = databaseService;
        _loggingService = loggingService;
    }

    /// <inheritdoc />
    public ValidationResult ValidateTask(TaskItem task)
    {
        var result = new ValidationResult();

        if (task == null)
        {
            result.AddError("Task cannot be null");
            return result;
        }

        // Validate title
        if (string.IsNullOrWhiteSpace(task.Title))
        {
            result.AddError("Task title is required");
        }
        else if (task.Title.Length > 200)
        {
            result.AddError("Task title cannot exceed 200 characters");
        }
        else if (task.Title.Trim() != task.Title)
        {
            result.AddWarning("Task title has leading or trailing whitespace");
        }

        // Validate description
        if (!string.IsNullOrEmpty(task.Description))
        {
            if (task.Description.Length > 1000)
            {
                result.AddError("Task description cannot exceed 1000 characters");
            }
        }

        // Validate due date
        if (task.DueDate == DateTime.MinValue)
        {
            result.AddError("Task due date is required");
        }
        else if (task.DueDate < DateTime.Now.AddMinutes(-5)) // Allow 5 minutes tolerance
        {
            result.AddWarning("Task due date is in the past");
        }

        // Validate priority
        if (!Enum.IsDefined(typeof(TaskPriority), task.Priority))
        {
            result.AddError("Invalid task priority");
        }

        // Validate completion state
        if (task.IsComplete && !task.CompletedDate.HasValue)
        {
            result.AddWarning("Completed task should have a completion date");
        }
        else if (!task.IsComplete && task.CompletedDate.HasValue)
        {
            result.AddWarning("Incomplete task should not have a completion date");
        }

        // Set overall validity
        result.IsValid = !result.Errors.Any();

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateTaskCreation(string title, string? description, DateTime dueDate, TaskPriority priority)
    {
        var tempTask = new TaskItem
        {
            Title = title ?? string.Empty,
            Description = description,
            DueDate = dueDate,
            Priority = priority
        };

        var result = ValidateTask(tempTask);

        // Additional validation for task creation
        if (dueDate <= DateTime.Now)
        {
            result.AddError("Due date must be in the future");
        }

        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateSearchTerm(string searchTerm)
    {
        var result = new ValidationResult();

        if (string.IsNullOrEmpty(searchTerm))
        {
            result.AddWarning("Search term is empty");
            result.IsValid = true;
            return result;
        }

        if (searchTerm.Length < 2)
        {
            result.AddWarning("Search term is very short, results may be limited");
        }

        if (searchTerm.Length > 100)
        {
            result.AddError("Search term is too long (maximum 100 characters)");
        }

        // Check for potentially problematic characters
        var problematicChars = new[] { '<', '>', '"', '\'', '&' };
        if (searchTerm.Any(c => problematicChars.Contains(c)))
        {
            result.AddWarning("Search term contains special characters that may affect results");
        }

        result.IsValid = !result.Errors.Any();
        return result;
    }

    /// <inheritdoc />
    public ValidationResult ValidateApplicationSettings()
    {
        var result = new ValidationResult();

        try
        {
            // Check if application data directory exists and is writable
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var appFolder = Path.Combine(appDataPath, "TaskMaster");

            if (!Directory.Exists(appFolder))
            {
                try
                {
                    Directory.CreateDirectory(appFolder);
                    result.AddWarning("Application data directory was created");
                }
                catch (Exception ex)
                {
                    result.AddError($"Cannot create application data directory: {ex.Message}");
                }
            }

            // Test write permissions
            if (Directory.Exists(appFolder))
            {
                var testFile = Path.Combine(appFolder, "test_write.tmp");
                try
                {
                    File.WriteAllText(testFile, "test");
                    File.Delete(testFile);
                }
                catch (Exception ex)
                {
                    result.AddError($"Cannot write to application data directory: {ex.Message}");
                }
            }

            // Check available disk space
            var drive = new DriveInfo(Path.GetPathRoot(appFolder) ?? "C:");
            if (drive.AvailableFreeSpace < 100 * 1024 * 1024) // 100 MB
            {
                result.AddWarning("Low disk space available (less than 100 MB)");
            }

            result.IsValid = !result.Errors.Any();
        }
        catch (Exception ex)
        {
            result.AddError($"Error validating application settings: {ex.Message}");
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<ValidationResult> ValidateDatabaseConnectionAsync()
    {
        var result = new ValidationResult();

        try
        {
            // Try to initialize the database
            await _databaseService.InitializeAsync();

            // Try to get task statistics (this will test basic database operations)
            var stats = await _databaseService.GetTaskStatisticsAsync();
            
            result.AddWarning($"Database connection successful. Found {stats.TotalTasks} total tasks.");
            result.IsValid = true;

            await _loggingService.LogAsync("Database validation completed successfully");
        }
        catch (Exception ex)
        {
            result.AddError($"Database connection failed: {ex.Message}");
            await _loggingService.LogErrorAsync("Database validation failed", ex);
        }

        return result;
    }
}

/// <summary>
/// Static helper class for common validations
/// </summary>
public static class ValidationHelper
{
    /// <summary>
    /// Validates that a string is not null or empty
    /// </summary>
    /// <param name="value">String to validate</param>
    /// <param name="fieldName">Name of the field being validated</param>
    /// <returns>Validation error message or null if valid</returns>
    public static string? ValidateRequired(string? value, string fieldName)
    {
        return string.IsNullOrWhiteSpace(value) ? $"{fieldName} is required" : null;
    }

    /// <summary>
    /// Validates string length
    /// </summary>
    /// <param name="value">String to validate</param>
    /// <param name="fieldName">Name of the field being validated</param>
    /// <param name="maxLength">Maximum allowed length</param>
    /// <param name="minLength">Minimum required length</param>
    /// <returns>Validation error message or null if valid</returns>
    public static string? ValidateLength(string? value, string fieldName, int maxLength, int minLength = 0)
    {
        if (string.IsNullOrEmpty(value))
        {
            return minLength > 0 ? $"{fieldName} is required" : null;
        }

        if (value.Length < minLength)
        {
            return $"{fieldName} must be at least {minLength} characters long";
        }

        if (value.Length > maxLength)
        {
            return $"{fieldName} cannot exceed {maxLength} characters";
        }

        return null;
    }

    /// <summary>
    /// Validates that a date is in the future
    /// </summary>
    /// <param name="date">Date to validate</param>
    /// <param name="fieldName">Name of the field being validated</param>
    /// <param name="allowToday">Whether today's date is allowed</param>
    /// <returns>Validation error message or null if valid</returns>
    public static string? ValidateFutureDate(DateTime date, string fieldName, bool allowToday = false)
    {
        var compareDate = allowToday ? DateTime.Today : DateTime.Now;
        
        if (date <= compareDate)
        {
            return $"{fieldName} must be in the future";
        }

        return null;
    }

    /// <summary>
    /// Validates an enum value
    /// </summary>
    /// <typeparam name="T">Enum type</typeparam>
    /// <param name="value">Value to validate</param>
    /// <param name="fieldName">Name of the field being validated</param>
    /// <returns>Validation error message or null if valid</returns>
    public static string? ValidateEnum<T>(T value, string fieldName) where T : Enum
    {
        return Enum.IsDefined(typeof(T), value) ? null : $"Invalid {fieldName} value";
    }
}
