using Microsoft.Extensions.DependencyInjection;
using System.Windows;
using TaskMaster.Services;

namespace TaskMaster.WPF;

/// <summary>
/// Main program entry point for TaskMaster WPF application
/// </summary>
public class Program
{
    /// <summary>
    /// Main entry point
    /// </summary>
    /// <param name="args">Command line arguments</param>
    [STAThread]
    public static void Main(string[] args)
    {
        try
        {
            // Create and configure services
            var services = ConfigureServices();
            
            // Create and run WPF application
            var app = new Application();
            var mainWindow = new MainWindow(services);
            
            app.Run(mainWindow);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Fatal error: {ex.Message}", "TaskMaster Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// Configures dependency injection services
    /// </summary>
    /// <returns>Configured service provider</returns>
    private static ServiceProvider ConfigureServices()
    {
        var services = new ServiceCollection();

        // Register core services
        services.AddSingleton<ILoggingService, LoggingService>();
        services.AddSingleton<IDatabaseService, DatabaseService>();
        services.AddSingleton<IValidationService, ValidationService>();
        services.AddSingleton<ITaskService, TaskService>();
        services.AddSingleton<IErrorHandlingService, ErrorHandlingService>();
        services.AddSingleton<IHealthCheckService, HealthCheckService>();

        // Register notification service without system tray for simplicity
        services.AddSingleton<INotificationService>(provider =>
        {
            var taskService = provider.GetRequiredService<ITaskService>();
            var loggingService = provider.GetRequiredService<ILoggingService>();
            return new NotificationService(taskService, loggingService, null);
        });

        return services.BuildServiceProvider();
    }
}
