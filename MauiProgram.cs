using Microsoft.Extensions.Logging;
using TaskMaster.Services;
using TaskMaster.ViewModels;
using TaskMaster.Views;

namespace TaskMaster;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                fonts.AddFont("Segoe-UI.ttf", "SegoeUI");
            });

        // Register Services
        builder.Services.AddSingleton<IDatabaseService, DatabaseService>();
        builder.Services.AddSingleton<ITaskService, TaskService>();
        builder.Services.AddSingleton<ILoggingService, LoggingService>();
        builder.Services.AddSingleton<ISystemTrayService, SystemTrayService>();
        builder.Services.AddSingleton<INotificationService, NotificationService>();
        builder.Services.AddSingleton<IErrorHandlingService, ErrorHandlingService>();
        builder.Services.AddSingleton<IValidationService, ValidationService>();
        builder.Services.AddSingleton<IHealthCheckService, HealthCheckService>();

        // Register ViewModels
        builder.Services.AddTransient<MainViewModel>();
        builder.Services.AddTransient<TaskCreationViewModel>();
        builder.Services.AddTransient<CompletedTasksViewModel>();

        // Register Views
        builder.Services.AddTransient<MainWindow>();
        builder.Services.AddTransient<TaskCreationPage>();
        builder.Services.AddTransient<CompletedTasksPage>();

#if DEBUG
        builder.Logging.AddDebug();
#endif

        return builder.Build();
    }
}
