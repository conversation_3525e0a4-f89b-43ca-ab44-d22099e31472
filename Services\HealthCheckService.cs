using System.Diagnostics;

namespace TaskMaster.Services;

/// <summary>
/// Implementation of application health check service
/// </summary>
public class HealthCheckService : IHealthCheckService
{
    private readonly IDatabaseService _databaseService;
    private readonly ILoggingService _loggingService;
    private readonly IValidationService _validationService;

    /// <summary>
    /// Initializes a new instance of HealthCheckService
    /// </summary>
    /// <param name="databaseService">Database service</param>
    /// <param name="loggingService">Logging service</param>
    /// <param name="validationService">Validation service</param>
    public HealthCheckService(IDatabaseService databaseService, ILoggingService loggingService, IValidationService validationService)
    {
        _databaseService = databaseService;
        _loggingService = loggingService;
        _validationService = validationService;
    }

    /// <inheritdoc />
    public async Task<HealthCheckResult> PerformHealthCheckAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new HealthCheckResult();

        try
        {
            // Check services
            var servicesResult = await CheckServicesAsync();
            result.AddDetail("Services", servicesResult.Status, servicesResult.Message);

            // Check database
            var databaseResult = await CheckDatabaseHealthAsync();
            result.AddDetail("Database", databaseResult.Status, databaseResult.Message);

            // Check file system
            var fileSystemResult = await CheckFileSystemHealthAsync();
            result.AddDetail("File System", fileSystemResult.Status, fileSystemResult.Message);

            // Check system resources
            var resourcesResult = await CheckSystemResourcesAsync();
            result.AddDetail("System Resources", resourcesResult.Status, resourcesResult.Message);

            // Set overall message
            result.Message = result.Status switch
            {
                HealthStatus.Healthy => "All systems are functioning normally",
                HealthStatus.Degraded => "Some systems are experiencing issues but application is functional",
                HealthStatus.Unhealthy => "Critical issues detected that may affect application functionality",
                _ => "Unknown health status"
            };

            await _loggingService.LogAsync($"Health check completed: {result.Status}");
        }
        catch (Exception ex)
        {
            result.Status = HealthStatus.Unhealthy;
            result.Message = $"Health check failed: {ex.Message}";
            await _loggingService.LogErrorAsync("Health check failed", ex);
        }
        finally
        {
            stopwatch.Stop();
            result.Duration = stopwatch.Elapsed;
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<HealthCheckResult> CheckServicesAsync()
    {
        try
        {
            // Check if all required services are available
            var services = new Dictionary<string, object?>
            {
                { "DatabaseService", _databaseService },
                { "LoggingService", _loggingService },
                { "ValidationService", _validationService }
            };

            var missingServices = services.Where(s => s.Value == null).Select(s => s.Key).ToList();

            if (missingServices.Any())
            {
                return HealthCheckResult.Unhealthy($"Missing services: {string.Join(", ", missingServices)}");
            }

            return HealthCheckResult.Healthy("All required services are available");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Service check failed", ex);
            return HealthCheckResult.Unhealthy($"Service check failed: {ex.Message}");
        }
    }

    /// <inheritdoc />
    public async Task<HealthCheckResult> CheckDatabaseHealthAsync()
    {
        try
        {
            var validationResult = await _validationService.ValidateDatabaseConnectionAsync();
            
            if (!validationResult.IsValid)
            {
                return HealthCheckResult.Unhealthy($"Database validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            // Try to perform a basic operation
            var stats = await _databaseService.GetTaskStatisticsAsync();
            
            return HealthCheckResult.Healthy($"Database is healthy. Total tasks: {stats.TotalTasks}");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("Database health check failed", ex);
            return HealthCheckResult.Unhealthy($"Database health check failed: {ex.Message}");
        }
    }

    /// <inheritdoc />
    public async Task<HealthCheckResult> CheckFileSystemHealthAsync()
    {
        try
        {
            var validationResult = _validationService.ValidateApplicationSettings();
            
            if (!validationResult.IsValid)
            {
                return HealthCheckResult.Unhealthy($"File system validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            if (validationResult.Warnings.Any())
            {
                return HealthCheckResult.Degraded($"File system has warnings: {string.Join(", ", validationResult.Warnings)}");
            }

            return HealthCheckResult.Healthy("File system is accessible and writable");
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("File system health check failed", ex);
            return HealthCheckResult.Unhealthy($"File system health check failed: {ex.Message}");
        }
    }

    /// <inheritdoc />
    public async Task<HealthCheckResult> CheckSystemResourcesAsync()
    {
        try
        {
            var result = HealthCheckResult.Healthy("System resources are adequate");

            // Check available memory
            var process = Process.GetCurrentProcess();
            var memoryUsageMB = process.WorkingSet64 / (1024 * 1024);
            
            if (memoryUsageMB > 500) // More than 500 MB
            {
                result.Status = HealthStatus.Degraded;
                result.Message = $"High memory usage: {memoryUsageMB} MB";
            }
            else if (memoryUsageMB > 1000) // More than 1 GB
            {
                result.Status = HealthStatus.Unhealthy;
                result.Message = $"Very high memory usage: {memoryUsageMB} MB";
            }

            // Check disk space
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var drive = new DriveInfo(Path.GetPathRoot(appDataPath) ?? "C:");
            var freeSpaceGB = drive.AvailableFreeSpace / (1024 * 1024 * 1024);

            if (freeSpaceGB < 1) // Less than 1 GB
            {
                result.Status = HealthStatus.Unhealthy;
                result.Message += $" Low disk space: {freeSpaceGB:F1} GB available";
            }
            else if (freeSpaceGB < 5) // Less than 5 GB
            {
                if (result.Status == HealthStatus.Healthy)
                {
                    result.Status = HealthStatus.Degraded;
                    result.Message = $"Low disk space: {freeSpaceGB:F1} GB available";
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            await _loggingService.LogErrorAsync("System resources health check failed", ex);
            return HealthCheckResult.Unhealthy($"System resources health check failed: {ex.Message}");
        }
    }

    /// <inheritdoc />
    public async Task<HealthCheckResult> ValidateStartupAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new HealthCheckResult();

        try
        {
            // Quick validation of critical components
            await _loggingService.LogAsync("Starting startup validation");

            // Check database initialization
            try
            {
                await _databaseService.InitializeAsync();
                result.AddDetail("Database Init", HealthStatus.Healthy, "Database initialized successfully");
            }
            catch (Exception ex)
            {
                result.AddDetail("Database Init", HealthStatus.Unhealthy, $"Database initialization failed: {ex.Message}");
            }

            // Check application settings
            var settingsValidation = _validationService.ValidateApplicationSettings();
            var settingsStatus = settingsValidation.IsValid ? HealthStatus.Healthy : HealthStatus.Unhealthy;
            result.AddDetail("App Settings", settingsStatus, settingsValidation.IsValid ? "Settings valid" : string.Join(", ", settingsValidation.Errors));

            // Set overall message
            result.Message = result.Status switch
            {
                HealthStatus.Healthy => "Startup validation completed successfully",
                HealthStatus.Degraded => "Startup validation completed with warnings",
                HealthStatus.Unhealthy => "Startup validation failed",
                _ => "Unknown startup validation status"
            };

            await _loggingService.LogAsync($"Startup validation completed: {result.Status}");
        }
        catch (Exception ex)
        {
            result.Status = HealthStatus.Unhealthy;
            result.Message = $"Startup validation failed: {ex.Message}";
            await _loggingService.LogErrorAsync("Startup validation failed", ex);
        }
        finally
        {
            stopwatch.Stop();
            result.Duration = stopwatch.Elapsed;
        }

        return result;
    }
}
