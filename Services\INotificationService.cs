using TaskMaster.Models;

namespace TaskMaster.Services;

/// <summary>
/// Interface for notification service in the TaskMaster application
/// </summary>
public interface INotificationService
{
    /// <summary>
    /// Event raised when the system tray icon is clicked
    /// </summary>
    event EventHandler? TrayIconClicked;

    /// <summary>
    /// Event raised when the user requests to show the main window
    /// </summary>
    event EventHandler? ShowMainWindowRequested;

    /// <summary>
    /// Event raised when the user requests to exit the application
    /// </summary>
    event EventHandler? ExitApplicationRequested;

    /// <summary>
    /// Initializes the notification service
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    Task InitializeAsync();

    /// <summary>
    /// Shows a notification for a task reminder
    /// </summary>
    /// <param name="task">Task to remind about</param>
    /// <returns>Task representing the async operation</returns>
    Task ShowTaskReminderAsync(TaskItem task);

    /// <summary>
    /// Shows a general notification
    /// </summary>
    /// <param name="title">Notification title</param>
    /// <param name="message">Notification message</param>
    /// <param name="isError">Whether this is an error notification</param>
    /// <returns>Task representing the async operation</returns>
    Task ShowNotificationAsync(string title, string message, bool isError = false);

    /// <summary>
    /// Shows a system tray balloon notification
    /// </summary>
    /// <param name="title">Balloon title</param>
    /// <param name="message">Balloon message</param>
    /// <param name="timeout">Timeout in milliseconds</param>
    /// <returns>Task representing the async operation</returns>
    Task ShowBalloonNotificationAsync(string title, string message, int timeout = 5000);

    /// <summary>
    /// Updates the system tray icon tooltip
    /// </summary>
    /// <param name="tooltip">New tooltip text</param>
    /// <returns>Task representing the async operation</returns>
    Task UpdateTrayTooltipAsync(string tooltip);

    /// <summary>
    /// Starts the reminder service that checks for due tasks
    /// </summary>
    void StartReminderService();

    /// <summary>
    /// Stops the reminder service
    /// </summary>
    void StopReminderService();

    /// <summary>
    /// Sets the reminder interval (how often to check for due tasks)
    /// </summary>
    /// <param name="interval">Check interval</param>
    void SetReminderInterval(TimeSpan interval);

    /// <summary>
    /// Sets how far in advance to remind about tasks
    /// </summary>
    /// <param name="reminderTime">Time before due date to remind</param>
    void SetReminderTime(TimeSpan reminderTime);

    /// <summary>
    /// Shows or hides the system tray icon
    /// </summary>
    /// <param name="visible">Whether the icon should be visible</param>
    void SetTrayIconVisible(bool visible);

    /// <summary>
    /// Checks for tasks that need reminders and shows notifications
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    Task CheckAndNotifyTaskRemindersAsync();

    /// <summary>
    /// Gets the current reminder settings
    /// </summary>
    /// <returns>Tuple containing reminder time and check interval</returns>
    (TimeSpan ReminderTime, TimeSpan CheckInterval) GetReminderSettings();

    /// <summary>
    /// Disposes the notification service
    /// </summary>
    void Dispose();
}
