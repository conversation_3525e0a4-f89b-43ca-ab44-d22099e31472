namespace TaskMaster.Services;

/// <summary>
/// Interface for global error handling service
/// </summary>
public interface IErrorHandlingService
{
    /// <summary>
    /// Event raised when an unhandled exception occurs
    /// </summary>
    event EventHandler<UnhandledExceptionEventArgs>? UnhandledException;

    /// <summary>
    /// Handles an exception and logs it appropriately
    /// </summary>
    /// <param name="exception">Exception to handle</param>
    /// <param name="context">Context where the exception occurred</param>
    /// <param name="showToUser">Whether to show the error to the user</param>
    /// <returns>Task representing the async operation</returns>
    Task HandleExceptionAsync(Exception exception, string context, bool showToUser = true);

    /// <summary>
    /// Handles a validation error
    /// </summary>
    /// <param name="message">Validation error message</param>
    /// <param name="context">Context where the validation error occurred</param>
    /// <returns>Task representing the async operation</returns>
    Task HandleValidationErrorAsync(string message, string context);

    /// <summary>
    /// Shows an error message to the user
    /// </summary>
    /// <param name="title">Error title</param>
    /// <param name="message">Error message</param>
    /// <param name="isRecoverable">Whether the error is recoverable</param>
    /// <returns>Task representing the async operation</returns>
    Task ShowErrorToUserAsync(string title, string message, bool isRecoverable = true);

    /// <summary>
    /// Gets a user-friendly error message from an exception
    /// </summary>
    /// <param name="exception">Exception to get message from</param>
    /// <returns>User-friendly error message</returns>
    string GetUserFriendlyErrorMessage(Exception exception);

    /// <summary>
    /// Determines if an exception is critical (requires app restart)
    /// </summary>
    /// <param name="exception">Exception to check</param>
    /// <returns>True if critical, false otherwise</returns>
    bool IsCriticalException(Exception exception);

    /// <summary>
    /// Initializes the global exception handling
    /// </summary>
    void InitializeGlobalExceptionHandling();
}

/// <summary>
/// Event args for unhandled exceptions
/// </summary>
public class UnhandledExceptionEventArgs : EventArgs
{
    /// <summary>
    /// The exception that occurred
    /// </summary>
    public Exception Exception { get; }

    /// <summary>
    /// Context where the exception occurred
    /// </summary>
    public string Context { get; }

    /// <summary>
    /// Whether the exception was handled
    /// </summary>
    public bool Handled { get; set; }

    /// <summary>
    /// Initializes a new instance of UnhandledExceptionEventArgs
    /// </summary>
    /// <param name="exception">The exception</param>
    /// <param name="context">The context</param>
    public UnhandledExceptionEventArgs(Exception exception, string context)
    {
        Exception = exception;
        Context = context;
    }
}
