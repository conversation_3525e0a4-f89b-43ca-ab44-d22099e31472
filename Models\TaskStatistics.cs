namespace TaskMaster.Models;

/// <summary>
/// Represents statistics about tasks in the application
/// </summary>
public class TaskStatistics
{
    /// <summary>
    /// Total number of tasks
    /// </summary>
    public int TotalTasks { get; set; }

    /// <summary>
    /// Number of pending (incomplete) tasks
    /// </summary>
    public int PendingTasks { get; set; }

    /// <summary>
    /// Number of completed tasks
    /// </summary>
    public int CompletedTasks { get; set; }

    /// <summary>
    /// Number of overdue tasks
    /// </summary>
    public int OverdueTasks { get; set; }

    /// <summary>
    /// Number of high priority tasks
    /// </summary>
    public int HighPriorityTasks { get; set; }

    /// <summary>
    /// Number of medium priority tasks
    /// </summary>
    public int MediumPriorityTasks { get; set; }

    /// <summary>
    /// Number of low priority tasks
    /// </summary>
    public int LowPriorityTasks { get; set; }

    /// <summary>
    /// Number of tasks due today
    /// </summary>
    public int TasksDueToday { get; set; }

    /// <summary>
    /// Number of tasks due this week
    /// </summary>
    public int TasksDueThisWeek { get; set; }

    /// <summary>
    /// Completion percentage (0-100)
    /// </summary>
    public double CompletionPercentage => TotalTasks > 0 ? (double)CompletedTasks / TotalTasks * 100 : 0;

    /// <summary>
    /// Creates task statistics from a collection of tasks
    /// </summary>
    /// <param name="tasks">Collection of tasks to analyze</param>
    /// <returns>TaskStatistics object</returns>
    public static TaskStatistics FromTasks(IEnumerable<TaskItem> tasks)
    {
        var taskList = tasks.ToList();
        var now = DateTime.Now;
        var today = now.Date;
        var endOfWeek = today.AddDays(7 - (int)today.DayOfWeek);

        return new TaskStatistics
        {
            TotalTasks = taskList.Count,
            PendingTasks = taskList.Count(t => !t.IsComplete),
            CompletedTasks = taskList.Count(t => t.IsComplete),
            OverdueTasks = taskList.Count(t => t.IsOverdue),
            HighPriorityTasks = taskList.Count(t => t.Priority == TaskPriority.High && !t.IsComplete),
            MediumPriorityTasks = taskList.Count(t => t.Priority == TaskPriority.Medium && !t.IsComplete),
            LowPriorityTasks = taskList.Count(t => t.Priority == TaskPriority.Low && !t.IsComplete),
            TasksDueToday = taskList.Count(t => !t.IsComplete && t.DueDate.Date == today),
            TasksDueThisWeek = taskList.Count(t => !t.IsComplete && t.DueDate.Date <= endOfWeek)
        };
    }
}

/// <summary>
/// Enumeration for task sorting options
/// </summary>
public enum TaskSortOption
{
    /// <summary>
    /// Sort by due date (soonest first)
    /// </summary>
    DueDate,
    
    /// <summary>
    /// Sort by priority (highest first)
    /// </summary>
    Priority,
    
    /// <summary>
    /// Sort by creation date (newest first)
    /// </summary>
    CreatedDate,
    
    /// <summary>
    /// Sort by title (alphabetical)
    /// </summary>
    Title
}

/// <summary>
/// Enumeration for task filter options
/// </summary>
public enum TaskFilterOption
{
    /// <summary>
    /// Show all tasks
    /// </summary>
    All,
    
    /// <summary>
    /// Show only pending tasks
    /// </summary>
    Pending,
    
    /// <summary>
    /// Show only completed tasks
    /// </summary>
    Completed,
    
    /// <summary>
    /// Show only overdue tasks
    /// </summary>
    Overdue,
    
    /// <summary>
    /// Show only high priority tasks
    /// </summary>
    HighPriority,
    
    /// <summary>
    /// Show only tasks due today
    /// </summary>
    DueToday,
    
    /// <summary>
    /// Show only tasks due this week
    /// </summary>
    DueThisWeek
}
