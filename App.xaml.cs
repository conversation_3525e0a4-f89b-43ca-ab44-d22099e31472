using TaskMaster.Services;
using TaskMaster.Views;

namespace TaskMaster;

public partial class App : Application
{
    private readonly ILoggingService _loggingService;
    private readonly INotificationService _notificationService;
    private readonly IDatabaseService _databaseService;
    private readonly IErrorHandlingService _errorHandlingService;

    public App(ILoggingService loggingService, INotificationService notificationService, IDatabaseService databaseService, IErrorHandlingService errorHandlingService)
    {
        InitializeComponent();

        _loggingService = loggingService;
        _notificationService = notificationService;
        _databaseService = databaseService;
        _errorHandlingService = errorHandlingService;
    }

    protected override Window CreateWindow(IActivationState? activationState)
    {
        var window = Handler?.MauiContext?.Services.GetService<MainWindow>() ?? new MainWindow();

        // Configure window properties
        window.Title = "TaskMaster - Task Reminder Application";
        window.MinimumWidth = 800;
        window.MinimumHeight = 600;
        window.Width = 1000;
        window.Height = 700;

        return window;
    }

    protected override async void OnStart()
    {
        base.OnStart();
        
        try
        {
            // Initialize global exception handling
            _errorHandlingService.InitializeGlobalExceptionHandling();

            // Initialize database
            await _databaseService.InitializeAsync();

            // Initialize notification service
            await _notificationService.InitializeAsync();

            // Start background services
            _notificationService.StartReminderService();

            await _loggingService.LogAsync("Application started successfully");
        }
        catch (Exception ex)
        {
            await _errorHandlingService.HandleExceptionAsync(ex, "Application startup", true);
        }
    }

    protected override async void OnSleep()
    {
        base.OnSleep();
        await _loggingService.LogAsync("Application entering sleep mode");
    }

    protected override async void OnResume()
    {
        base.OnResume();
        await _loggingService.LogAsync("Application resumed from sleep");
    }
}
