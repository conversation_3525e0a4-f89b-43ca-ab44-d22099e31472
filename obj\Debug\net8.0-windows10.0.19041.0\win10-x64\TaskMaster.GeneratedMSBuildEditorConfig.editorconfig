is_global = true
build_property.TargetFramework = net8.0-windows10.0.19041.0
build_property.TargetPlatformMinVersion = 10.0.17763.0
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 9A19103F-16F7-4668-BE54-9A1E7A4F7556
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = TaskMaster
build_property.ProjectDir = G:\vscodeapps\taskremindergem\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.CsWinRTAotOptimizerEnabled = true
build_property.CsWinRTAotExportsEnabled = 
build_property.CsWinRTRcwFactoryFallbackGeneratorForceOptIn = 
build_property.CsWinRTRcwFactoryFallbackGeneratorForceOptOut = 
build_property.CsWinRTCcwLookupTableGeneratorEnabled = true
build_property.CsWinRTMergeReferencedActivationFactories = 
build_property.CsWinRTAotWarningLevel = 

[G:/vscodeapps/taskremindergem/App.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = TaskMaster.App.xaml
build_metadata.AdditionalFiles.TargetPath = App.xaml
build_metadata.AdditionalFiles.RelativePath = App.xaml

[G:/vscodeapps/taskremindergem/Resources/Styles/Colors.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = TaskMaster.Resources.Styles.Colors.xaml
build_metadata.AdditionalFiles.TargetPath = Resources\Styles\Colors.xaml
build_metadata.AdditionalFiles.RelativePath = Resources\Styles\Colors.xaml

[G:/vscodeapps/taskremindergem/Resources/Styles/Styles.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = TaskMaster.Resources.Styles.Styles.xaml
build_metadata.AdditionalFiles.TargetPath = Resources\Styles\Styles.xaml
build_metadata.AdditionalFiles.RelativePath = Resources\Styles\Styles.xaml

[G:/vscodeapps/taskremindergem/Views/CompletedTasksPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = TaskMaster.Views.CompletedTasksPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\CompletedTasksPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\CompletedTasksPage.xaml

[G:/vscodeapps/taskremindergem/Views/MainWindow.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = TaskMaster.Views.MainWindow.xaml
build_metadata.AdditionalFiles.TargetPath = Views\MainWindow.xaml
build_metadata.AdditionalFiles.RelativePath = Views\MainWindow.xaml

[G:/vscodeapps/taskremindergem/Views/TaskCreationPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = TaskMaster.Views.TaskCreationPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\TaskCreationPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\TaskCreationPage.xaml
