using Microsoft.EntityFrameworkCore;
using TaskMaster.Models;

namespace TaskMaster.Services;

/// <summary>
/// Entity Framework database context for TaskMaster application
/// </summary>
public class TaskDbContext : DbContext
{
    private readonly string _databasePath;

    /// <summary>
    /// DbSet for TaskItem entities
    /// </summary>
    public DbSet<TaskItem> Tasks { get; set; } = null!;

    /// <summary>
    /// Initializes a new instance of TaskDbContext
    /// </summary>
    public TaskDbContext()
    {
        // Get the application data folder path
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        var appFolder = Path.Combine(appDataPath, "TaskMaster");
        
        // Create directory if it doesn't exist
        if (!Directory.Exists(appFolder))
        {
            Directory.CreateDirectory(appFolder);
        }
        
        _databasePath = Path.Combine(appFolder, "TaskMaster.db");
    }

    /// <summary>
    /// Configures the database connection
    /// </summary>
    /// <param name="optionsBuilder">Options builder</param>
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            optionsBuilder.UseSqlite($"Data Source={_databasePath}");
        }
    }

    /// <summary>
    /// Configures the model relationships and constraints
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure TaskItem entity
        modelBuilder.Entity<TaskItem>(entity =>
        {
            // Primary key
            entity.HasKey(e => e.Id);

            // Configure properties
            entity.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(200);

            entity.Property(e => e.Description)
                .HasMaxLength(1000);

            entity.Property(e => e.DueDate)
                .IsRequired();

            entity.Property(e => e.Priority)
                .IsRequired()
                .HasConversion<int>(); // Store enum as integer

            entity.Property(e => e.IsComplete)
                .IsRequired()
                .HasDefaultValue(false);

            entity.Property(e => e.CreatedDate)
                .IsRequired()
                .HasDefaultValueSql("datetime('now')");

            entity.Property(e => e.CompletedDate)
                .IsRequired(false);

            // Create indexes for better query performance
            entity.HasIndex(e => e.DueDate)
                .HasDatabaseName("IX_Tasks_DueDate");

            entity.HasIndex(e => e.Priority)
                .HasDatabaseName("IX_Tasks_Priority");

            entity.HasIndex(e => e.IsComplete)
                .HasDatabaseName("IX_Tasks_IsComplete");

            entity.HasIndex(e => e.CreatedDate)
                .HasDatabaseName("IX_Tasks_CreatedDate");

            // Composite index for common queries
            entity.HasIndex(e => new { e.IsComplete, e.DueDate })
                .HasDatabaseName("IX_Tasks_IsComplete_DueDate");

            entity.HasIndex(e => new { e.IsComplete, e.Priority })
                .HasDatabaseName("IX_Tasks_IsComplete_Priority");
        });
    }

    /// <summary>
    /// Gets the database file path
    /// </summary>
    /// <returns>Full path to the database file</returns>
    public string GetDatabasePath()
    {
        return _databasePath;
    }

    /// <summary>
    /// Ensures the database is created and up to date
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    public async Task EnsureDatabaseCreatedAsync()
    {
        try
        {
            await Database.EnsureCreatedAsync();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to create database at {_databasePath}", ex);
        }
    }

    /// <summary>
    /// Applies any pending migrations
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    public async Task MigrateDatabaseAsync()
    {
        try
        {
            await Database.MigrateAsync();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to migrate database at {_databasePath}", ex);
        }
    }
}
