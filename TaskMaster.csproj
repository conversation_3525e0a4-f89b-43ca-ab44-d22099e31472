<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net8.0-windows10.0.19041.0</TargetFrameworks>
		<OutputType>Exe</OutputType>
		<RootNamespace>TaskMaster</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<UseWinUI>true</UseWinUI>

		<!-- Display Version -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<!-- App Identifier -->
		<ApplicationId>com.taskmaster.app</ApplicationId>
		<ApplicationIdGuid>C2482DA0-E085-4B53-9196-6781C3AACBF8</ApplicationIdGuid>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<WindowsPackageType>None</WindowsPackageType>
	</PropertyGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Maui.Controls" Version="8.0.91" />
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="8.0.91" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8" />
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Models\" />
	  <Folder Include="ViewModels\" />
	  <Folder Include="Views\" />
	  <Folder Include="Services\" />
	  <Folder Include="Resources\" />
	</ItemGroup>

	<!-- Exclude console-specific files from MAUI build -->
	<ItemGroup>
		<Compile Remove="Program.cs" />
		<Compile Remove="Tests\**\*.cs" />
		<None Include="Program.cs" />
		<None Include="Tests\**\*.cs" />
	</ItemGroup>

</Project>
