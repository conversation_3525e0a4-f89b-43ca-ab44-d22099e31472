<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0-windows</TargetFramework>
		<OutputType>WinExe</OutputType>
		<RootNamespace>TaskMaster</RootNamespace>
		<UseWPF>true</UseWPF>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<ApplicationIcon>Resources\AppIcon\taskmaster.ico</ApplicationIcon>
		<AssemblyTitle>TaskMaster</AssemblyTitle>
		<AssemblyDescription>Task Reminder Application</AssemblyDescription>
		<AssemblyVersion>*******</AssemblyVersion>
		<FileVersion>*******</FileVersion>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8" />
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Models\" />
	  <Folder Include="ViewModels\" />
	  <Folder Include="Views\" />
	  <Folder Include="Services\" />
	  <Folder Include="Resources\" />
	</ItemGroup>

	<!-- Exclude XAML files for console-only build -->
	<ItemGroup>
		<None Remove="**/*.xaml" />
		<Compile Remove="App.xaml.cs" />
		<Compile Remove="Views/*.xaml.cs" />
		<Compile Remove="ViewModels/*.cs" />
	</ItemGroup>

</Project>
