﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly manifestVersion="1.0" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:winrtv1="urn:schemas-microsoft-com:winrt.v1" xmlns="urn:schemas-microsoft-com:asm.v1">
    <asmv3:file name="Microsoft.Windows.ApplicationModel.Resources.dll">
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.Resources.KnownResourceQualifierName" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.Resources.ResourceCandidate" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.Resources.ResourceLoader" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.Resources.ResourceManager" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="Microsoft.WindowsAppRuntime.dll">
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.DynamicDependency.AddPackageDependencyOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.DynamicDependency.CreatePackageDependencyOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyContext" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyRank" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentResult" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentInitializeOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.WindowsAppRuntime.ReleaseInfo" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.ApplicationModel.WindowsAppRuntime.RuntimeInfo" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.AppLifecycle.ActivationRegistrationManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.AppLifecycle.AppInstance" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.AppNotifications.AppNotificationManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.AppNotifications.AppNotification" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.AppNotifications.AppNotificationProgressData" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.AppNotifications.AppNotificationActivatedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.AppNotifications.Builder.AppNotificationTextProperties" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.AppNotifications.Builder.AppNotificationButton" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.AppNotifications.Builder.AppNotificationComboBox" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.AddPackageOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.EnsureReadyOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.PackageDeploymentManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.PackageDeploymentResult" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.PackageRuntimeManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.PackageSet" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.PackageSetItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.PackageSetItemRuntimeDisposition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.PackageSetRuntimeDisposition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.PackageVolume" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.ProvisionPackageOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.RegisterPackageOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.RemovePackageOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Management.Deployment.StagePackageOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.PushNotifications.PushNotificationChannel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.PushNotifications.PushNotificationCreateChannelResult" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.PushNotifications.PushNotificationActivationInfo" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.PushNotifications.PushNotificationReceivedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.PushNotifications.PushNotificationRegistrationToken" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.PushNotifications.PushNotificationManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Security.AccessControl.SecurityDescriptorHelpers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.System.EnvironmentManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.System.Power.PowerManager" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="Microsoft.UI.Xaml.dll">
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.AdaptiveTrigger" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Application" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.AnnotationPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.AutomationAnnotation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.AutomationElementIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.AutomationProperties" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.DockPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.DragPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.DropTargetPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.ExpandCollapsePatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.GridItemPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.GridPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.MultipleViewPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AppBarAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AppBarButtonAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AppBarToggleButtonAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AutomationPeerAnnotation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AutoSuggestBoxAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ButtonAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ButtonBaseAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.CalendarDatePickerAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.CheckBoxAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ComboBoxAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ComboBoxItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ComboBoxItemDataAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.DatePickerAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.FlipViewAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.FlipViewItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.FlipViewItemDataAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.FlyoutPresenterAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.FrameworkElementAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.GridViewAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.GridViewHeaderItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.GridViewItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.GridViewItemDataAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.GroupItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.HubAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.HubSectionAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.HyperlinkButtonAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ImageAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ItemsControlAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListBoxAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListBoxItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListBoxItemDataAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListViewAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListViewBaseAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListViewBaseHeaderItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListViewHeaderItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListViewItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListViewItemDataAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.MediaPlayerElementAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.MediaTransportControlsAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.MenuFlyoutItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.MenuFlyoutPresenterAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PasswordBoxAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RadioButtonAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RangeBaseAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RepeatButtonAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RichEditBoxAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RichTextBlockAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RichTextBlockOverflowAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ScrollBarAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ScrollViewerAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.SelectorAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.SelectorItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.SemanticZoomAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.SliderAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TextBlockAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TextBoxAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ThumbAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TimePickerAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ToggleButtonAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ToggleMenuFlyoutItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ToggleSwitchAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.RangeValuePatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.ScrollPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.SelectionItemPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.SelectionPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.SpreadsheetItemPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.StylesPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.TableItemPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.TablePatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.TogglePatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.TransformPattern2Identifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.TransformPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.ValuePatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.WindowPatternIdentifiers" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.BringIntoViewOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.BrushTransition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.ColorDisplayNameHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.ColorPaletteResources" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AppBar" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AppBarButton" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AppBarElementContainer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AppBarSeparator" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AppBarToggleButton" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AutoSuggestBox" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AutoSuggestBoxQuerySubmittedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AutoSuggestBoxSuggestionChosenEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AutoSuggestBoxTextChangedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.BitmapIcon" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.BitmapIconSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Border" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Button" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.CalendarDatePicker" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.CalendarView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.CalendarViewDayItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Canvas" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.CheckBox" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ChoosingGroupHeaderContainerEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ChoosingItemContainerEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ColumnDefinition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ComboBox" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ComboBoxItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.CommandBar" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.CommandBarOverflowPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.CommandingContainer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ContentControl" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ContentDialog" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ContentPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Control" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ControlTemplate" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.DataTemplateSelector" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.DatePicker" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.DragItemsStartingEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.DynamicOverflowItemsChangingEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.FlipView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.FlipViewItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Flyout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.FlyoutPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.FontIcon" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.FontIconSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Frame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Grid" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.GridView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.GridViewHeaderItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.GridViewItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.GroupItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.GroupStyle" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.GroupStyleSelector" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Hub" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.HubSection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.HubSectionHeaderClickEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.HyperlinkButton" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.IconElement" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.IconSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.IconSourceElement" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Image" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.InputValidationCommand" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.InputValidationContext" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.InputValidationError" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemClickEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemsControl" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemsPanelTemplate" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemsPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemsStackPanel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemsWrapGrid" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ListBox" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ListBoxItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ListView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ListViewBase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ListViewHeaderItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ListViewItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ListViewPersistenceHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MediaPlayerElement" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MediaPlayerPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MediaTransportControls" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MediaTransportControlsHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MenuFlyout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MenuFlyoutItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MenuFlyoutPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MenuFlyoutSeparator" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MenuFlyoutSubItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Page" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Panel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.PasswordBox" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.PathIcon" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.PathIconSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ButtonBase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.CalendarPanel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.CarouselPanel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.DragCompletedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.DragDeltaEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.DragStartedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.FlyoutBase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.FlyoutShowOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.GeneratorPositionHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.GridViewItemPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.LayoutInformation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ListViewItemPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.Popup" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.RangeBase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.RepeatButton" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollBar" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.Selector" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.SelectorItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.Thumb" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.TickBar" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ToggleButton" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RadioButton" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RelativePanel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RichEditBox" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RichTextBlock" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RichTextBlockOverflow" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RowDefinition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ScrollContentPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ScrollViewer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ScrollViewerViewChangedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SelectionChangedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SemanticZoom" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SemanticZoomLocation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SemanticZoomViewChangedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Slider" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SplitView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.StackPanel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.StyleSelector" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SwapChainBackgroundPanel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SwapChainPanel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SymbolIcon" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SymbolIconSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TextBlock" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TextBox" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TimePicker" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ToggleMenuFlyoutItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ToggleSwitch" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ToolTip" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ToolTipService" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.UserControl" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.VariableSizedWrapGrid" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Viewbox" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.VirtualizingStackPanel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.WrapGrid" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Core.Direct.XamlDirect" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.CornerRadiusHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Data.Binding" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Data.BindingBase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Data.BindingOperations" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Data.CollectionViewSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Data.CurrentChangingEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Data.DataErrorsChangedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Data.ItemIndexRange" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Data.PropertyChangedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Data.RelativeSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.DataTemplate" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.DataTemplateKey" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.DependencyObject" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.DependencyObjectCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.DependencyProperty" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.DispatcherTimer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.Block" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.Bold" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.Glyphs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.Hyperlink" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.Inline" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.InlineUIContainer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.Italic" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.LineBreak" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.Paragraph" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.Run" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.Span" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.TextElement" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.TextHighlighter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.Typography" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Documents.Underline" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.DurationHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.DxamlCoreTestHooks" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.ElementFactoryGetArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.ElementFactoryRecycleArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.ElementSoundPlayer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.EventTrigger" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.FrameworkElement" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.FrameworkElementEx" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.FrameworkTemplate" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.FrameworkView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.FrameworkViewSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.GridLengthHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Hosting.DesktopWindowXamlSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Hosting.ElementCompositionPreview" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Hosting.WindowsXamlManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Hosting.XamlIslandRoot" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Hosting.XamlSourceFocusNavigationRequest" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Hosting.XamlSourceFocusNavigationResult" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.AccessKeyDisplayDismissedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.AccessKeyDisplayRequestedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.AccessKeyInvokedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.AccessKeyManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.ContextRequestedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.DoubleTappedRoutedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.FindNextElementOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.FocusManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.HoldingRoutedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.InputManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.InputScope" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.InputScopeName" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.KeyboardAccelerator" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.ManipulationCompletedRoutedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.ManipulationDeltaRoutedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.ManipulationInertiaStartingRoutedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.ManipulationPivot" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.ManipulationStartedRoutedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.ManipulationStartingRoutedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.RightTappedRoutedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.StandardUICommand" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.TappedRoutedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Input.XamlUICommand" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.InteractionBase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Internal.LayoutTransitionElementUtilities" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Internal.SecondaryContentRelationship" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Interop.NotifyCollectionChangedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Markup.MarkupExtension" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Markup.ProvideValueTargetProperty" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Markup.XamlBinaryWriter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Markup.XamlBindingHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Markup.XamlMarkupHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Markup.XamlReader" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.AddDeleteThemeTransition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.BackEase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.BasicConnectedAnimationConfiguration" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.BeginStoryboard" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.BounceEase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.CircleEase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ColorAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ColorAnimationUsingKeyFrames" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ColorKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ColorKeyFrameCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ConnectedAnimationService" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ContentThemeTransition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.CubicEase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DirectConnectedAnimationConfiguration" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DiscreteColorKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DiscreteDoubleKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DiscreteObjectKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DiscretePointKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DoubleAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DoubleAnimationUsingKeyFrames" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DoubleKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DoubleKeyFrameCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DragItemThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DragOverThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DrillInThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DrillOutThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DropTargetItemThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.EasingColorKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.EasingDoubleKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.EasingFunctionBase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.EasingPointKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.EdgeUIThemeTransition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ElasticEase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.EntranceThemeTransition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ExponentialEase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.FadeInThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.FadeOutThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.GravityConnectedAnimationConfiguration" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.KeySpline" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.KeyTimeHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.LinearColorKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.LinearDoubleKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.LinearPointKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.NavigationTransitionInfo" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ObjectAnimationUsingKeyFrames" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ObjectKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ObjectKeyFrameCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.PaneThemeTransition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.PointAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.PointAnimationUsingKeyFrames" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.PointerDownThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.PointerUpThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.PointKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.PointKeyFrameCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.PopInThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.PopOutThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.PopupThemeTransition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.PowerEase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.QuadraticEase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.QuarticEase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.QuinticEase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ReorderThemeTransition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.RepeatBehaviorHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.RepositionThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.RepositionThemeTransition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.SineEase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.SplineColorKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.SplineDoubleKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.SplinePointKeyFrame" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.SplitCloseThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.SplitOpenThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.Storyboard" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.SwipeBackThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.SwipeHintThemeAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ThemeAnimationBase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.Timeline" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.TimelineCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.Transition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.TransitionCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.ArcSegment" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.BezierSegment" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.BitmapCache" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Brush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.BrushCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.CacheMode" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.CompositeTransform" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.CompositionTarget" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.DoubleCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.EllipseGeometry" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.FontFamily" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.GeneralTransform" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Geometry" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.GeometryCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.GeometryGroup" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.GradientBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.GradientStop" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.GradientStopCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.ImageBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Imaging.BitmapImage" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Imaging.BitmapSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Imaging.RenderTargetBitmap" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Imaging.SoftwareBitmapSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Imaging.SurfaceImageSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Imaging.SvgImageSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Imaging.VirtualSurfaceImageSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Imaging.WriteableBitmap" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Imaging.XamlRenderingBackgroundTask" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.LinearGradientBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.LineGeometry" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.LineSegment" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.LoadedImageSurface" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Matrix3DProjection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.MatrixHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.MatrixTransform" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Media3D.CompositeTransform3D" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Media3D.Matrix3DHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Media3D.PerspectiveTransform3D" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Media3D.Transform3D" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.PathFigure" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.PathFigureCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.PathGeometry" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.PathSegmentCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.PlaneProjection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.PointCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.PolyBezierSegment" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.PolyLineSegment" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.PolyQuadraticBezierSegment" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Projection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.QuadraticBezierSegment" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.RectangleGeometry" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.RotateTransform" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.ScaleTransform" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.SkewTransform" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.SolidColorBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.SystemBackdrop" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.ThemeShadow" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.TileBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.TransformCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.TransformGroup" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.TranslateTransform" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.VisualTreeHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.XamlCompositionBrushBase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.XamlLight" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Navigation.FrameNavigationOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Navigation.PageStackEntry" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.PanelEx" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.PointHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Printing.AddPagesEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Printing.GetPreviewPageEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Printing.PaginateEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Printing.PrintDocument" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.PropertyMetadata" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.PropertyPath" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.RectHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.ResourceDictionary" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Resources.CustomXamlResourceLoader" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.RoutedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.ScalarTransition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Setter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.SetterBaseCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Shapes.Ellipse" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Shapes.Line" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Shapes.Path" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Shapes.Polygon" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Shapes.Polyline" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Shapes.Rectangle" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Shapes.Shape" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.SizeHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.StateTrigger" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.StateTriggerBase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Style" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.TargetPropertyPath" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.ThicknessHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.TriggerActionCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.UIElement" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.UIElementWeakCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Vector3Transition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.VisualState" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.VisualStateChangedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.VisualStateGroup" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.VisualStateManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.VisualTransition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Window" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.WindowChrome" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.XamlIsland" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="Microsoft.UI.Xaml.Controls.dll">
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.AnimatedIconTestHooks" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.ButtonInteraction" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.DisplayRegionHelperTestApi" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.ItemsViewTestHooks" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.LayoutsTestHooks" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.MUXControlsTestHooks" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.PullToRefreshHelperTestApi" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.RadioButtonsTestHooks" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.RepeaterTestHooks" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.ScrollPresenterTestHooks" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.ScrollViewerIRefreshInfoProviderAdapter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.ScrollViewTestHooks" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.SelectorBarTestHooks" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.SliderInteraction" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.SpectrumBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.SplitButtonTestApi" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.SwipeTestHooks" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Controls.TeachingTipTestHooks" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Media.AcrylicTestApi" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Media.MaterialHelperTestApi" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Media.RevealBorderLight" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Media.RevealBrushTestApi" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Media.RevealHoverLight" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Media.RevealTestApi" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Private.Media.XamlAmbientLight" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AnimatedVisualPlayerAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.BreadcrumbBarItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ColorPickerSliderAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ColorSpectrumAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.DropDownButtonAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ExpanderAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.InfoBarAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ItemContainerAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ItemsViewAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.MenuBarAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.MenuBarItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.NavigationViewAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.NavigationViewItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.NumberBoxAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PagerControlAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PersonPictureAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PipsPagerAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ProgressBarAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RadioButtonsAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RatingControlAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RepeaterAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ScrollPresenterAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.SelectorBarItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.SplitButtonAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TabViewAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TabViewItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TeachingTipAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ToggleSplitButtonAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TreeViewItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TreeViewItemDataAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TreeViewListAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.WebView2AutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedIcon" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedIconSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedAcceptVisualSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedBackVisualSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronDownSmallVisualSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronRightDownSmallVisualSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronUpDownSmallVisualSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedFindVisualSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedGlobalNavigationButtonVisualSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedSettingsVisualSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnnotatedScrollBar" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.AnnotatedScrollBarLabel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.BreadcrumbBar" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.BreadcrumbBarItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ColorPicker" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.CommandBarFlyout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.DropDownButton" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ElementFactory" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Expander" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.FlowLayout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.FlowLayoutState" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ImageIcon" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ImageIconSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.IndexPath" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.InfoBadge" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.InfoBadgeTemplateSettings" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.InfoBar" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.InfoBarTemplateSettings" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemCollectionTransitionProvider" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemContainer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemsRepeater" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemsRepeaterScrollHost" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemsSourceView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemsView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.LayoutPanel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.LinedFlowLayout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.LinedFlowLayoutItemCollectionTransitionProvider" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MapControl" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MapElementsLayer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MapIcon" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MenuBar" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MenuBarItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.MenuBarItemFlyout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.NavigationView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.NavigationViewItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.NavigationViewItemBase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.NavigationViewItemHeader" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.NavigationViewItemInvokedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.NavigationViewItemSeparator" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.NonVirtualizingLayout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.NonVirtualizingLayoutContext" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.NumberBox" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.PagerControl" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.PagerControlTemplateSettings" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ParallaxView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.PersonPicture" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.PipsPager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.AutoSuggestBoxHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ColorPickerSlider" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ColumnMajorUniformToLargestGridLayout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ComboBoxHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBar" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarAutomationProperties" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterConverter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.MonochromaticOverlayPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenterTemplateSettings" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.RepeatedScrollSnapPoint" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.RepeatedZoomSnapPoint" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollControllerAddScrollVelocityRequestedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollControllerPanRequestedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollControllerScrollByRequestedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollControllerScrollToRequestedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollSnapPoint" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.TabViewListView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ZoomSnapPoint" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ProgressBar" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ProgressRing" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RadioButtons" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RadioMenuFlyoutItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RatingControl" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RatingItemFontInfo" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RatingItemImageInfo" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RatingItemInfo" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RecyclePool" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RecyclingElementFactory" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RefreshContainer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RefreshVisualizer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.RevealListViewItemPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ScrollingScrollOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ScrollingZoomOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ScrollView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SelectionModel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SelectorBar" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SelectorBarItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SplitButton" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.StackLayout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.StackLayoutState" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SwipeControl" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SwipeItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.SwipeItems" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TabView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TabViewItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TabViewItemTemplateSettings" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TeachingTip" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TeachingTipTemplateSettings" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TextCommandBarFlyout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ToggleSplitButton" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TreeView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TreeViewItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TreeViewList" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TreeViewNode" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TwoPaneView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.UniformGridLayout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.UniformGridLayoutState" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.VirtualizingLayout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.WebView2" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.XamlControlsResources" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.AcrylicBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.DesktopAcrylicBackdrop" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.MicaBackdrop" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.RadialGradientBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.RevealBackgroundBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.RevealBorderBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.RevealBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.XamlTypeInfo.XamlControlsXamlMetaDataProvider" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="Microsoft.UI.Xaml.Phone.dll">
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PivotAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PivotItemAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PivotItemDataAutomationPeer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.DatePickedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.DatePickerFlyout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.DatePickerFlyoutItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.DatePickerFlyoutPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ItemsPickedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.ListPickerFlyout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.PickerConfirmedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.PickerFlyout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Pivot" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.PivotItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.PivotItemEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.JumpListItemBackgroundConverter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.JumpListItemForegroundConverter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.LoopingSelector" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.PickerFlyoutBase" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.PivotHeaderItem" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.PivotHeaderPanel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.PivotPanel" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TimePickedEventArgs" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TimePickerFlyout" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Controls.TimePickerFlyoutPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.CommonNavigationTransitionInfo" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.ContinuumNavigationTransitionInfo" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.DrillInNavigationTransitionInfo" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.EntranceNavigationTransitionInfo" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.NavigationThemeTransition" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.SlideNavigationTransitionInfo" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Xaml.Media.Animation.SuppressNavigationTransitionInfo" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="WinUIEdit.dll">
        <winrtv1:activatableClass name="Microsoft.UI.Text.FontWeights" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Text.TextConstants" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="Microsoft.Web.WebView2.Core.dll">
        <winrtv1:activatableClass name="Microsoft.Web.WebView2.Core.CoreWebView2CompositionController" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Web.WebView2.Core.CoreWebView2ControllerWindowReference" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Web.WebView2.Core.CoreWebView2CustomSchemeRegistration" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Web.WebView2.Core.CoreWebView2Environment" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="CoreMessagingXP.dll">
        <winrtv1:activatableClass name="Microsoft.UI.Dispatching.DispatcherExitDeferral" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Dispatching.DispatcherQueue" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Dispatching.DispatcherQueueController" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="dcompi.dll">
        <winrtv1:activatableClass name="Microsoft.UI.ColorHelper" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Colors" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.AnimationController" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionApiInformation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionCapabilities" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionClip" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionDrawingSurface" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionEasingFunction" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionEffectSourceParameter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionGeometry" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionGradientBrush" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionLight" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionObject" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionPath" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionProjectedShadowCasterCollection" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionShadow" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionShape" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionTransform" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.CompositionVirtualDrawingSurface" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Compositor" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.ContainerVisual" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Core.CompositorController" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Diagnostics.CompositionDebugSettings" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Effects.SceneLightingEffect" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Experimental.ExpCompositionVisualSurface" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Interactions.CompositionConditionalValue" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTracker" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTrackerInertiaModifier" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTrackerInertiaMotion" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTrackerInertiaNaturalMotion" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTrackerInertiaRestingValue" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTrackerVector2InertiaModifier" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTrackerVector2InertiaNaturalMotion" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Interactions.VisualInteractionSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.KeyFrameAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.NaturalMotionAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.ScalarNaturalMotionAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Scenes.SceneComponent" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Scenes.SceneMaterial" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Scenes.SceneMaterialInput" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Scenes.SceneMesh" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Scenes.SceneMeshRendererComponent" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Scenes.SceneMetallicRoughnessMaterial" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Scenes.SceneNode" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Scenes.SceneObject" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Scenes.ScenePbrMaterial" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Scenes.SceneRendererComponent" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Scenes.SceneSurfaceMaterialInput" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Scenes.SceneVisual" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Vector2NaturalMotionAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Vector3NaturalMotionAnimation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.Visual" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="Microsoft.Graphics.Display.dll">
        <winrtv1:activatableClass name="Microsoft.Graphics.Display.DisplayInformation" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="Microsoft.UI.dll">
        <winrtv1:activatableClass name="Microsoft.UI.System.ThemeSettings" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="Microsoft.UI.Input.dll">
        <winrtv1:activatableClass name="Microsoft.UI.Content.ContentAppWindowBridge" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.ContentCoordinateConverter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.ContentExternalBackdropLink" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.ContentExternalOutputLink" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.ContentIsland" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.ContentIslandEnvironment" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.ContentSite" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.ContentSiteEnvironment" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.ContentSiteEnvironmentView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.ContentSiteView" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.CoreWindowSiteBridge" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.DesktopChildSiteBridge" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.DesktopSiteBridge" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.ProcessStarter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Content.SystemVisualSiteBridge" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.DragDrop.DragDropManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.DragDrop.DragOperation" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.Experimental.ExpFocusController" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.Experimental.ExpFocusNavigationHost" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.Experimental.ExpFocusNavigationRequest" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.Experimental.ExpPointerPoint" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.FocusNavigationRequest" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.GestureRecognizer" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputActivationListener" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputCursor" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputCustomCursor" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputDesktopResourceCursor" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputDesktopNamedResourceCursor" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputFocusController" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputFocusNavigationHost" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputKeyboardSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputLightDismissAction" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputObject" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputPreTranslateKeyboardSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputNonClientPointerSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputPointerSource" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.InputSystemCursor" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.Interop.PenDeviceInterop" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Input.PointerPredictor" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="Microsoft.UI.Windowing.dll">
        <winrtv1:activatableClass name="Microsoft.UI.Windowing.AppWindow" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Windowing.AppWindowPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Windowing.CompactOverlayPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Windowing.DisplayArea" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Windowing.FullScreenPresenter" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Windowing.OverlappedPresenter" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="Microsoft.UI.Windowing.Core.dll">
        <winrtv1:activatableClass name="Microsoft.UI.Windowing.AppWindowTitleBar" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="wuceffectsi.dll">
        <winrtv1:activatableClass name="Microsoft.UI.Composition.SystemBackdrops.DesktopAcrylicController" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.SystemBackdrops.MicaController" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.UI.Composition.SystemBackdrops.SystemBackdropConfiguration" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
    <asmv3:file name="Microsoft.Windows.Widgets.dll">
        <winrtv1:activatableClass name="Microsoft.Windows.Widgets.Providers.WidgetUpdateRequestOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Widgets.Providers.WidgetManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Widgets.Feeds.Providers.FeedManager" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Widgets.Feeds.Providers.CustomQueryParametersUpdateOptions" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceResponse" threadingModel="both"></winrtv1:activatableClass>
        <winrtv1:activatableClass name="Microsoft.Windows.Widgets.Notifications.FeedAnnouncement" threadingModel="both"></winrtv1:activatableClass>
    </asmv3:file>
</assembly>