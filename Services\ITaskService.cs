using TaskMaster.Models;

namespace TaskMaster.Services;

/// <summary>
/// Interface for task management service in the TaskMaster application
/// </summary>
public interface ITaskService
{
    /// <summary>
    /// Event raised when tasks are updated
    /// </summary>
    event EventHandler? TasksUpdated;

    /// <summary>
    /// Event raised when a task is added
    /// </summary>
    event EventHandler<TaskItem>? TaskAdded;

    /// <summary>
    /// Event raised when a task is completed
    /// </summary>
    event EventHandler<TaskItem>? TaskCompleted;

    /// <summary>
    /// Event raised when a task is deleted
    /// </summary>
    event EventHandler<int>? TaskDeleted;

    /// <summary>
    /// Gets all tasks
    /// </summary>
    /// <returns>Collection of all tasks</returns>
    Task<IEnumerable<TaskItem>> GetAllTasksAsync();

    /// <summary>
    /// Gets pending tasks with optional sorting and filtering
    /// </summary>
    /// <param name="sortOption">How to sort the tasks</param>
    /// <param name="filterOption">How to filter the tasks</param>
    /// <returns>Collection of filtered and sorted pending tasks</returns>
    Task<IEnumerable<TaskItem>> GetPendingTasksAsync(TaskSortOption sortOption = TaskSortOption.DueDate, TaskFilterOption filterOption = TaskFilterOption.Pending);

    /// <summary>
    /// Gets completed tasks
    /// </summary>
    /// <returns>Collection of completed tasks</returns>
    Task<IEnumerable<TaskItem>> GetCompletedTasksAsync();

    /// <summary>
    /// Gets a specific task by ID
    /// </summary>
    /// <param name="id">Task ID</param>
    /// <returns>Task item or null if not found</returns>
    Task<TaskItem?> GetTaskByIdAsync(int id);

    /// <summary>
    /// Creates a new task
    /// </summary>
    /// <param name="title">Task title</param>
    /// <param name="description">Task description (optional)</param>
    /// <param name="dueDate">Due date and time</param>
    /// <param name="priority">Task priority</param>
    /// <returns>The created task</returns>
    Task<TaskItem> CreateTaskAsync(string title, string? description, DateTime dueDate, TaskPriority priority);

    /// <summary>
    /// Updates an existing task
    /// </summary>
    /// <param name="task">Task to update</param>
    /// <returns>Task representing the async operation</returns>
    Task UpdateTaskAsync(TaskItem task);

    /// <summary>
    /// Deletes a task
    /// </summary>
    /// <param name="id">ID of the task to delete</param>
    /// <returns>True if deleted successfully</returns>
    Task<bool> DeleteTaskAsync(int id);

    /// <summary>
    /// Marks a task as complete
    /// </summary>
    /// <param name="id">ID of the task to complete</param>
    /// <returns>True if marked as complete successfully</returns>
    Task<bool> CompleteTaskAsync(int id);

    /// <summary>
    /// Marks a task as incomplete
    /// </summary>
    /// <param name="id">ID of the task to mark as incomplete</param>
    /// <returns>True if marked as incomplete successfully</returns>
    Task<bool> UncompleteTaskAsync(int id);

    /// <summary>
    /// Gets task statistics
    /// </summary>
    /// <returns>Task statistics</returns>
    Task<TaskStatistics> GetTaskStatisticsAsync();

    /// <summary>
    /// Searches tasks by title or description
    /// </summary>
    /// <param name="searchTerm">Search term</param>
    /// <returns>Collection of matching tasks</returns>
    Task<IEnumerable<TaskItem>> SearchTasksAsync(string searchTerm);

    /// <summary>
    /// Gets tasks that need reminders (due within specified time)
    /// </summary>
    /// <param name="reminderTime">Time before due date to remind</param>
    /// <returns>Collection of tasks needing reminders</returns>
    Task<IEnumerable<TaskItem>> GetTasksNeedingRemindersAsync(TimeSpan reminderTime);

    /// <summary>
    /// Gets overdue tasks
    /// </summary>
    /// <returns>Collection of overdue tasks</returns>
    Task<IEnumerable<TaskItem>> GetOverdueTasksAsync();

    /// <summary>
    /// Applies sorting to a collection of tasks
    /// </summary>
    /// <param name="tasks">Tasks to sort</param>
    /// <param name="sortOption">Sort option</param>
    /// <returns>Sorted tasks</returns>
    IEnumerable<TaskItem> ApplySorting(IEnumerable<TaskItem> tasks, TaskSortOption sortOption);

    /// <summary>
    /// Applies filtering to a collection of tasks
    /// </summary>
    /// <param name="tasks">Tasks to filter</param>
    /// <param name="filterOption">Filter option</param>
    /// <returns>Filtered tasks</returns>
    IEnumerable<TaskItem> ApplyFiltering(IEnumerable<TaskItem> tasks, TaskFilterOption filterOption);
}
