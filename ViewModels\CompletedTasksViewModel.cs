using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using TaskMaster.Models;
using TaskMaster.Services;

namespace TaskMaster.ViewModels;

/// <summary>
/// View model for the completed tasks view
/// </summary>
public partial class CompletedTasksViewModel : BaseViewModel
{
    /// <summary>
    /// Initializes a new instance of CompletedTasksViewModel
    /// </summary>
    /// <param name="taskService">Task service</param>
    /// <param name="loggingService">Logging service</param>
    public CompletedTasksViewModel(ITaskService taskService, ILoggingService loggingService)
        : base(taskService, loggingService)
    {
        Title = "Completed Tasks";
        
        // Initialize collections
        CompletedTasks = new ObservableCollection<TaskItem>();
        
        // Subscribe to task service events
        _taskService.TasksUpdated += OnTasksUpdated;
        _taskService.TaskCompleted += OnTaskCompleted;
        _taskService.TaskDeleted += OnTaskDeleted;
        
        // Initialize with default sort option
        SelectedSortOption = CompletedTaskSortOption.CompletedDate;
    }

    #region Properties

    /// <summary>
    /// Collection of completed tasks
    /// </summary>
    public ObservableCollection<TaskItem> CompletedTasks { get; }

    [ObservableProperty]
    private TaskItem? _selectedTask;

    [ObservableProperty]
    private CompletedTaskSortOption _selectedSortOption;

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    [ObservableProperty]
    private int _totalCompletedTasks;

    [ObservableProperty]
    private DateTime? _filterFromDate;

    [ObservableProperty]
    private DateTime? _filterToDate;

    /// <summary>
    /// Available sort options for completed tasks
    /// </summary>
    public CompletedTaskSortOption[] SortOptions { get; } = Enum.GetValues<CompletedTaskSortOption>();

    #endregion

    #region Commands

    /// <summary>
    /// Command to load completed tasks
    /// </summary>
    [RelayCommand]
    private async Task LoadCompletedTasksAsync()
    {
        await ExecuteAsync(async () =>
        {
            var tasks = await _taskService.GetCompletedTasksAsync();
            
            // Apply filtering
            var filteredTasks = ApplyFiltering(tasks);
            
            // Apply sorting
            var sortedTasks = ApplySorting(filteredTasks);
            
            CompletedTasks.Clear();
            foreach (var task in sortedTasks)
            {
                CompletedTasks.Add(task);
            }
            
            TotalCompletedTasks = CompletedTasks.Count;
            StatusMessage = $"Loaded {CompletedTasks.Count} completed tasks";
            
        }, "Loading completed tasks...", "Failed to load completed tasks");
    }

    /// <summary>
    /// Command to refresh completed tasks
    /// </summary>
    [RelayCommand]
    private async Task RefreshTasksAsync()
    {
        await LoadCompletedTasksAsync();
    }

    /// <summary>
    /// Command to search completed tasks
    /// </summary>
    [RelayCommand]
    private async Task SearchTasksAsync()
    {
        await ExecuteAsync(async () =>
        {
            IEnumerable<TaskItem> tasks;
            
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                tasks = await _taskService.GetCompletedTasksAsync();
            }
            else
            {
                var allTasks = await _taskService.SearchTasksAsync(SearchText);
                tasks = allTasks.Where(t => t.IsComplete);
            }
            
            // Apply filtering
            var filteredTasks = ApplyFiltering(tasks);
            
            // Apply sorting
            var sortedTasks = ApplySorting(filteredTasks);
            
            CompletedTasks.Clear();
            foreach (var task in sortedTasks)
            {
                CompletedTasks.Add(task);
            }
            
            StatusMessage = $"Found {CompletedTasks.Count} completed tasks";
            
        }, "Searching completed tasks...", "Failed to search completed tasks");
    }

    /// <summary>
    /// Command to clear search
    /// </summary>
    [RelayCommand]
    private async Task ClearSearchAsync()
    {
        SearchText = string.Empty;
        FilterFromDate = null;
        FilterToDate = null;
        await LoadCompletedTasksAsync();
    }

    /// <summary>
    /// Command to mark selected task as incomplete (reopen)
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanReopenTask))]
    private async Task ReopenTaskAsync()
    {
        if (SelectedTask == null) return;

        await ExecuteAsync(async () =>
        {
            var success = await _taskService.UncompleteTaskAsync(SelectedTask.Id);
            if (success)
            {
                StatusMessage = $"Reopened task: {SelectedTask.Title}";
                SelectedTask = null;
            }
            else
            {
                throw new InvalidOperationException("Failed to reopen task");
            }
        }, "Reopening task...", "Failed to reopen task");
    }

    /// <summary>
    /// Command to delete selected completed task
    /// </summary>
    [RelayCommand(CanExecute = nameof(CanDeleteTask))]
    private async Task DeleteTaskAsync()
    {
        if (SelectedTask == null) return;

        await ExecuteAsync(async () =>
        {
            var success = await _taskService.DeleteTaskAsync(SelectedTask.Id);
            if (success)
            {
                StatusMessage = $"Deleted completed task: {SelectedTask.Title}";
                SelectedTask = null;
            }
            else
            {
                throw new InvalidOperationException("Failed to delete task");
            }
        }, "Deleting task...", "Failed to delete task");
    }

    /// <summary>
    /// Command to set filter to show tasks completed today
    /// </summary>
    [RelayCommand]
    private async Task FilterTodayAsync()
    {
        FilterFromDate = DateTime.Today;
        FilterToDate = DateTime.Today.AddDays(1).AddTicks(-1);
        await LoadCompletedTasksAsync();
    }

    /// <summary>
    /// Command to set filter to show tasks completed this week
    /// </summary>
    [RelayCommand]
    private async Task FilterThisWeekAsync()
    {
        var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
        FilterFromDate = startOfWeek;
        FilterToDate = startOfWeek.AddDays(7).AddTicks(-1);
        await LoadCompletedTasksAsync();
    }

    /// <summary>
    /// Command to set filter to show tasks completed this month
    /// </summary>
    [RelayCommand]
    private async Task FilterThisMonthAsync()
    {
        var startOfMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
        FilterFromDate = startOfMonth;
        FilterToDate = startOfMonth.AddMonths(1).AddTicks(-1);
        await LoadCompletedTasksAsync();
    }

    #endregion

    #region Command Can Execute Methods

    private bool CanReopenTask() => SelectedTask != null && !IsBusy;
    private bool CanDeleteTask() => SelectedTask != null && !IsBusy;

    #endregion

    #region Event Handlers

    private async void OnTasksUpdated(object? sender, EventArgs e)
    {
        await LoadCompletedTasksAsync();
    }

    private async void OnTaskCompleted(object? sender, TaskItem task)
    {
        await LoadCompletedTasksAsync();
        StatusMessage = $"Task completed: {task.Title}";
    }

    private async void OnTaskDeleted(object? sender, int taskId)
    {
        await LoadCompletedTasksAsync();
        StatusMessage = "Task deleted";
    }

    #endregion

    #region Property Changed Handlers

    partial void OnSelectedSortOptionChanged(CompletedTaskSortOption value)
    {
        _ = LoadCompletedTasksAsync();
    }

    partial void OnFilterFromDateChanged(DateTime? value)
    {
        if (value.HasValue)
        {
            _ = LoadCompletedTasksAsync();
        }
    }

    partial void OnFilterToDateChanged(DateTime? value)
    {
        if (value.HasValue)
        {
            _ = LoadCompletedTasksAsync();
        }
    }

    #endregion

    #region Private Methods

    /// <summary>
    /// Applies filtering to completed tasks
    /// </summary>
    /// <param name="tasks">Tasks to filter</param>
    /// <returns>Filtered tasks</returns>
    private IEnumerable<TaskItem> ApplyFiltering(IEnumerable<TaskItem> tasks)
    {
        var filteredTasks = tasks.AsEnumerable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(SearchText))
        {
            var searchTerm = SearchText.ToLower();
            filteredTasks = filteredTasks.Where(t => 
                t.Title.ToLower().Contains(searchTerm) ||
                (t.Description != null && t.Description.ToLower().Contains(searchTerm)));
        }

        // Apply date range filter
        if (FilterFromDate.HasValue)
        {
            filteredTasks = filteredTasks.Where(t => 
                t.CompletedDate.HasValue && t.CompletedDate.Value.Date >= FilterFromDate.Value.Date);
        }

        if (FilterToDate.HasValue)
        {
            filteredTasks = filteredTasks.Where(t => 
                t.CompletedDate.HasValue && t.CompletedDate.Value.Date <= FilterToDate.Value.Date);
        }

        return filteredTasks;
    }

    /// <summary>
    /// Applies sorting to completed tasks
    /// </summary>
    /// <param name="tasks">Tasks to sort</param>
    /// <returns>Sorted tasks</returns>
    private IEnumerable<TaskItem> ApplySorting(IEnumerable<TaskItem> tasks)
    {
        return SelectedSortOption switch
        {
            CompletedTaskSortOption.CompletedDate => tasks.OrderByDescending(t => t.CompletedDate),
            CompletedTaskSortOption.Title => tasks.OrderBy(t => t.Title),
            CompletedTaskSortOption.Priority => tasks.OrderByDescending(t => t.Priority).ThenByDescending(t => t.CompletedDate),
            CompletedTaskSortOption.CreatedDate => tasks.OrderByDescending(t => t.CreatedDate),
            _ => tasks.OrderByDescending(t => t.CompletedDate)
        };
    }

    #endregion

    /// <summary>
    /// Initializes the view model
    /// </summary>
    public async Task InitializeAsync()
    {
        await LoadCompletedTasksAsync();
    }
}

/// <summary>
/// Sort options for completed tasks
/// </summary>
public enum CompletedTaskSortOption
{
    /// <summary>
    /// Sort by completion date (newest first)
    /// </summary>
    CompletedDate,
    
    /// <summary>
    /// Sort by title (alphabetical)
    /// </summary>
    Title,
    
    /// <summary>
    /// Sort by priority (highest first)
    /// </summary>
    Priority,
    
    /// <summary>
    /// Sort by creation date (newest first)
    /// </summary>
    CreatedDate
}
