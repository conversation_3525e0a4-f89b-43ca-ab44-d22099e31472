namespace TaskMaster.Services;

/// <summary>
/// Interface for application health check service
/// </summary>
public interface IHealthCheckService
{
    /// <summary>
    /// Performs a comprehensive health check of the application
    /// </summary>
    /// <returns>Health check result</returns>
    Task<HealthCheckResult> PerformHealthCheckAsync();

    /// <summary>
    /// Checks if all required services are available
    /// </summary>
    /// <returns>Service availability result</returns>
    Task<HealthCheckResult> CheckServicesAsync();

    /// <summary>
    /// Checks database health
    /// </summary>
    /// <returns>Database health result</returns>
    Task<HealthCheckResult> CheckDatabaseHealthAsync();

    /// <summary>
    /// Checks file system permissions and availability
    /// </summary>
    /// <returns>File system health result</returns>
    Task<HealthCheckResult> CheckFileSystemHealthAsync();

    /// <summary>
    /// Checks system resources (memory, disk space)
    /// </summary>
    /// <returns>System resources health result</returns>
    Task<HealthCheckResult> CheckSystemResourcesAsync();

    /// <summary>
    /// Runs a quick startup validation
    /// </summary>
    /// <returns>Startup validation result</returns>
    Task<HealthCheckResult> ValidateStartupAsync();
}

/// <summary>
/// Represents the result of a health check
/// </summary>
public class HealthCheckResult
{
    /// <summary>
    /// Overall health status
    /// </summary>
    public HealthStatus Status { get; set; } = HealthStatus.Healthy;

    /// <summary>
    /// List of health check details
    /// </summary>
    public List<HealthCheckDetail> Details { get; set; } = new();

    /// <summary>
    /// Overall health check message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Time when the health check was performed
    /// </summary>
    public DateTime CheckTime { get; set; } = DateTime.Now;

    /// <summary>
    /// Duration of the health check
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// Creates a healthy result
    /// </summary>
    /// <param name="message">Success message</param>
    /// <returns>Healthy health check result</returns>
    public static HealthCheckResult Healthy(string message = "All systems operational")
    {
        return new HealthCheckResult
        {
            Status = HealthStatus.Healthy,
            Message = message
        };
    }

    /// <summary>
    /// Creates a degraded result
    /// </summary>
    /// <param name="message">Degraded message</param>
    /// <returns>Degraded health check result</returns>
    public static HealthCheckResult Degraded(string message)
    {
        return new HealthCheckResult
        {
            Status = HealthStatus.Degraded,
            Message = message
        };
    }

    /// <summary>
    /// Creates an unhealthy result
    /// </summary>
    /// <param name="message">Error message</param>
    /// <returns>Unhealthy health check result</returns>
    public static HealthCheckResult Unhealthy(string message)
    {
        return new HealthCheckResult
        {
            Status = HealthStatus.Unhealthy,
            Message = message
        };
    }

    /// <summary>
    /// Adds a detail to the health check result
    /// </summary>
    /// <param name="name">Detail name</param>
    /// <param name="status">Detail status</param>
    /// <param name="message">Detail message</param>
    public void AddDetail(string name, HealthStatus status, string message)
    {
        Details.Add(new HealthCheckDetail
        {
            Name = name,
            Status = status,
            Message = message
        });

        // Update overall status if this detail is worse
        if (status > Status)
        {
            Status = status;
        }
    }

    /// <summary>
    /// Gets a formatted summary of the health check
    /// </summary>
    /// <returns>Formatted health check summary</returns>
    public string GetSummary()
    {
        var summary = $"Health Check: {Status} - {Message}\n";
        summary += $"Checked at: {CheckTime:yyyy-MM-dd HH:mm:ss}\n";
        summary += $"Duration: {Duration.TotalMilliseconds:F0}ms\n\n";

        if (Details.Any())
        {
            summary += "Details:\n";
            foreach (var detail in Details)
            {
                summary += $"  {detail.Name}: {detail.Status} - {detail.Message}\n";
            }
        }

        return summary;
    }
}

/// <summary>
/// Represents a detailed health check item
/// </summary>
public class HealthCheckDetail
{
    /// <summary>
    /// Name of the health check item
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Status of the health check item
    /// </summary>
    public HealthStatus Status { get; set; } = HealthStatus.Healthy;

    /// <summary>
    /// Message describing the health check result
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Additional data related to the health check
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();
}

/// <summary>
/// Health status enumeration
/// </summary>
public enum HealthStatus
{
    /// <summary>
    /// System is healthy and functioning normally
    /// </summary>
    Healthy = 0,

    /// <summary>
    /// System is functioning but with some issues
    /// </summary>
    Degraded = 1,

    /// <summary>
    /// System is not functioning properly
    /// </summary>
    Unhealthy = 2
}
