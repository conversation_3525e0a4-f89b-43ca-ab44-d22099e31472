using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TaskMaster.Models;

/// <summary>
/// Represents a task item in the TaskMaster application
/// </summary>
[Table("Tasks")]
public class TaskItem
{
    /// <summary>
    /// Unique identifier for the task
    /// </summary>
    [Key]
    public int Id { get; set; }

    /// <summary>
    /// Brief, descriptive name for the task
    /// </summary>
    [Required]
    [StringLength(200, MinimumLength = 1, ErrorMessage = "Title must be between 1 and 200 characters")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Optional detailed explanation of the task
    /// </summary>
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// The specific date and time the task is due
    /// </summary>
    [Required]
    public DateTime DueDate { get; set; }

    /// <summary>
    /// Priority level of the task (Low, Medium, High)
    /// </summary>
    [Required]
    public TaskPriority Priority { get; set; } = TaskPriority.Medium;

    /// <summary>
    /// Boolean status indicating if the task is complete
    /// </summary>
    public bool IsComplete { get; set; } = false;

    /// <summary>
    /// Date and time when the task was created
    /// </summary>
    public DateTime CreatedDate { get; set; } = DateTime.Now;

    /// <summary>
    /// Date and time when the task was completed (null if not completed)
    /// </summary>
    public DateTime? CompletedDate { get; set; }

    /// <summary>
    /// Indicates if the task is overdue
    /// </summary>
    [NotMapped]
    public bool IsOverdue => !IsComplete && DateTime.Now > DueDate;

    /// <summary>
    /// Gets the time remaining until the task is due
    /// </summary>
    [NotMapped]
    public TimeSpan TimeRemaining => DueDate - DateTime.Now;

    /// <summary>
    /// Gets a user-friendly string representation of the time remaining
    /// </summary>
    [NotMapped]
    public string TimeRemainingText
    {
        get
        {
            if (IsComplete)
                return "Completed";

            var timeRemaining = TimeRemaining;
            
            if (timeRemaining.TotalMinutes < 0)
                return "Overdue";
            
            if (timeRemaining.TotalDays >= 1)
                return $"{(int)timeRemaining.TotalDays} day(s)";
            
            if (timeRemaining.TotalHours >= 1)
                return $"{(int)timeRemaining.TotalHours} hour(s)";
            
            return $"{(int)timeRemaining.TotalMinutes} minute(s)";
        }
    }

    /// <summary>
    /// Gets the priority as a display-friendly string
    /// </summary>
    [NotMapped]
    public string PriorityText => Priority.ToString();

    /// <summary>
    /// Gets the priority color for UI display
    /// </summary>
    [NotMapped]
    public string PriorityColor => Priority switch
    {
        TaskPriority.High => "#FF4444",
        TaskPriority.Medium => "#FFA500",
        TaskPriority.Low => "#4CAF50",
        _ => "#6C757D"
    };

    /// <summary>
    /// Validates the task item
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(Title) &&
               Title.Length <= 200 &&
               (string.IsNullOrEmpty(Description) || Description.Length <= 1000) &&
               DueDate > DateTime.MinValue;
    }

    /// <summary>
    /// Marks the task as complete
    /// </summary>
    public void MarkAsComplete()
    {
        IsComplete = true;
        CompletedDate = DateTime.Now;
    }

    /// <summary>
    /// Marks the task as incomplete
    /// </summary>
    public void MarkAsIncomplete()
    {
        IsComplete = false;
        CompletedDate = null;
    }

    /// <summary>
    /// Creates a copy of the current task
    /// </summary>
    /// <returns>A new TaskItem with the same properties</returns>
    public TaskItem Clone()
    {
        return new TaskItem
        {
            Title = Title,
            Description = Description,
            DueDate = DueDate,
            Priority = Priority,
            IsComplete = IsComplete,
            CreatedDate = CreatedDate,
            CompletedDate = CompletedDate
        };
    }

    public override string ToString()
    {
        return $"{Title} - Due: {DueDate:g} - Priority: {Priority}";
    }
}
