{"version": 3, "targets": {"net8.0-windows10.0.19041": {"CommunityToolkit.Mvvm/8.2.2": {"type": "package", "compile": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets": {}}}, "Humanizer.Core/2.14.1": {"type": "package", "compile": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"type": "package", "build": {"build/_._": {}}}, "Microsoft.CodeAnalysis.Common/4.5.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3", "System.Collections.Immutable": "6.0.0", "System.Reflection.Metadata": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.5.0]"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "[4.5.0]", "Microsoft.CodeAnalysis.Common": "[4.5.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.5.0]"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "[4.5.0]", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3", "System.Threading.Channels": "6.0.0"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Data.Sqlite.Core/8.0.8": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "compile": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore/8.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.8", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.8", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.8": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.8": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Design/8.0.8": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.8", "Microsoft.Extensions.DependencyModel": "8.0.1", "Mono.TextTemplating": "2.2.1"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"related": ".xml"}}, "build": {"build/net8.0/Microsoft.EntityFrameworkCore.Design.props": {}}}, "Microsoft.EntityFrameworkCore.Relational/8.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "8.0.8", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Sqlite/8.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "8.0.8", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.6"}, "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/8.0.8": {"type": "package", "dependencies": {"Microsoft.Data.Sqlite.Core": "8.0.8", "Microsoft.EntityFrameworkCore.Relational": "8.0.8", "Microsoft.Extensions.DependencyModel": "8.0.1"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Tools/8.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Design": "8.0.8"}, "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/8.0.1": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.4"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Graphics.Win2D/1.2.0": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK": "1.5.240227000"}, "compile": {"lib/net6.0-windows10.0.19041.0/Microsoft.Graphics.Canvas.Interop.dll": {}}, "runtime": {"lib/net6.0-windows10.0.19041.0/Microsoft.Graphics.Canvas.Interop.dll": {}}, "build": {"build/net6.0-windows10.0.19041.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Graphics.Canvas.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Graphics.Canvas.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Graphics.Canvas.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "compile": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Maui.Controls/8.0.91": {"type": "package", "dependencies": {"Microsoft.Maui.Controls.Build.Tasks": "8.0.91", "Microsoft.Maui.Controls.Core": "8.0.91", "Microsoft.Maui.Controls.Xaml": "8.0.91", "Microsoft.Maui.Resizetizer": "8.0.91"}}, "Microsoft.Maui.Controls.Build.Tasks/8.0.91": {"type": "package", "dependencies": {"Microsoft.Maui.Controls.Core": "8.0.91", "Microsoft.Maui.Controls.Xaml": "8.0.91"}, "build": {"buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.props": {}, "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.targets": {}}}, "Microsoft.Maui.Controls.Compatibility/8.0.91": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Maui.Controls.Core": "8.0.91", "Microsoft.Maui.Controls.Xaml": "8.0.91"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.dll": {"related": ".pdb;.pri;.xml"}}}, "Microsoft.Maui.Controls.Core/8.0.91": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Maui.Core": "8.0.91"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.dll": {"related": ".pdb;.pri;.xml"}}, "resource": {"lib/net8.0-windows10.0.19041/ar/Microsoft.Maui.Controls.resources.dll": {"locale": "ar"}, "lib/net8.0-windows10.0.19041/ca/Microsoft.Maui.Controls.resources.dll": {"locale": "ca"}, "lib/net8.0-windows10.0.19041/cs/Microsoft.Maui.Controls.resources.dll": {"locale": "cs"}, "lib/net8.0-windows10.0.19041/da/Microsoft.Maui.Controls.resources.dll": {"locale": "da"}, "lib/net8.0-windows10.0.19041/de/Microsoft.Maui.Controls.resources.dll": {"locale": "de"}, "lib/net8.0-windows10.0.19041/el/Microsoft.Maui.Controls.resources.dll": {"locale": "el"}, "lib/net8.0-windows10.0.19041/es/Microsoft.Maui.Controls.resources.dll": {"locale": "es"}, "lib/net8.0-windows10.0.19041/fi/Microsoft.Maui.Controls.resources.dll": {"locale": "fi"}, "lib/net8.0-windows10.0.19041/fr/Microsoft.Maui.Controls.resources.dll": {"locale": "fr"}, "lib/net8.0-windows10.0.19041/he/Microsoft.Maui.Controls.resources.dll": {"locale": "he"}, "lib/net8.0-windows10.0.19041/hi/Microsoft.Maui.Controls.resources.dll": {"locale": "hi"}, "lib/net8.0-windows10.0.19041/hr/Microsoft.Maui.Controls.resources.dll": {"locale": "hr"}, "lib/net8.0-windows10.0.19041/hu/Microsoft.Maui.Controls.resources.dll": {"locale": "hu"}, "lib/net8.0-windows10.0.19041/id/Microsoft.Maui.Controls.resources.dll": {"locale": "id"}, "lib/net8.0-windows10.0.19041/it/Microsoft.Maui.Controls.resources.dll": {"locale": "it"}, "lib/net8.0-windows10.0.19041/ja/Microsoft.Maui.Controls.resources.dll": {"locale": "ja"}, "lib/net8.0-windows10.0.19041/ko/Microsoft.Maui.Controls.resources.dll": {"locale": "ko"}, "lib/net8.0-windows10.0.19041/ms/Microsoft.Maui.Controls.resources.dll": {"locale": "ms"}, "lib/net8.0-windows10.0.19041/nb/Microsoft.Maui.Controls.resources.dll": {"locale": "nb"}, "lib/net8.0-windows10.0.19041/nl/Microsoft.Maui.Controls.resources.dll": {"locale": "nl"}, "lib/net8.0-windows10.0.19041/pl/Microsoft.Maui.Controls.resources.dll": {"locale": "pl"}, "lib/net8.0-windows10.0.19041/pt-BR/Microsoft.Maui.Controls.resources.dll": {"locale": "pt-BR"}, "lib/net8.0-windows10.0.19041/pt/Microsoft.Maui.Controls.resources.dll": {"locale": "pt"}, "lib/net8.0-windows10.0.19041/ro/Microsoft.Maui.Controls.resources.dll": {"locale": "ro"}, "lib/net8.0-windows10.0.19041/ru/Microsoft.Maui.Controls.resources.dll": {"locale": "ru"}, "lib/net8.0-windows10.0.19041/sk/Microsoft.Maui.Controls.resources.dll": {"locale": "sk"}, "lib/net8.0-windows10.0.19041/sv/Microsoft.Maui.Controls.resources.dll": {"locale": "sv"}, "lib/net8.0-windows10.0.19041/th/Microsoft.Maui.Controls.resources.dll": {"locale": "th"}, "lib/net8.0-windows10.0.19041/tr/Microsoft.Maui.Controls.resources.dll": {"locale": "tr"}, "lib/net8.0-windows10.0.19041/uk/Microsoft.Maui.Controls.resources.dll": {"locale": "uk"}, "lib/net8.0-windows10.0.19041/vi/Microsoft.Maui.Controls.resources.dll": {"locale": "vi"}, "lib/net8.0-windows10.0.19041/zh-HK/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-HK"}, "lib/net8.0-windows10.0.19041/zh-Hans/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0-windows10.0.19041/zh-Hant/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Maui.Controls.Xaml/8.0.91": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Maui.Controls.Core": "8.0.91", "Microsoft.Maui.Core": "8.0.91"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Core/8.0.91": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.Maui.Essentials": "8.0.91", "Microsoft.Maui.Graphics": "8.0.91", "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop": "8.0.91", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.756", "Microsoft.WindowsAppSDK": "1.5.240802000"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.dll": {"related": ".pdb;.pri;.xml"}}, "build": {"buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.props": {}, "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.targets": {}}}, "Microsoft.Maui.Essentials/8.0.91": {"type": "package", "dependencies": {"Microsoft.Maui.Graphics": "8.0.91", "Microsoft.WindowsAppSDK": "1.5.240802000"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Essentials.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Essentials.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Graphics/8.0.91": {"type": "package", "dependencies": {"Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.1", "Microsoft.WindowsAppSDK": "1.5.240802000"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop/8.0.91": {"type": "package", "dependencies": {"Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.Maui.Graphics": "8.0.91", "Microsoft.WindowsAppSDK": "1.5.240802000"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll": {"related": ".pdb"}}}, "Microsoft.Maui.Resizetizer/8.0.91": {"type": "package", "build": {"buildTransitive/Microsoft.Maui.Resizetizer.props": {}, "buildTransitive/Microsoft.Maui.Resizetizer.targets": {}}}, "Microsoft.Windows.SDK.BuildTools/10.0.22621.756": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.WindowsAppSDK/1.5.240802000": {"type": "package", "dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.22621.756"}, "compile": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}, "runtimeTargets": {"runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win10-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win10-arm64"}, "runtimes/win10-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win10-x64"}, "runtimes/win10-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {"assetType": "native", "rid": "win10-x86"}}}, "Mono.TextTemplating/2.2.1": {"type": "package", "dependencies": {"System.CodeDom": "4.4.0"}, "compile": {"lib/netstandard2.0/Mono.TextTemplating.dll": {}}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"type": "package", "dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.6", "SQLitePCLRaw.provider.e_sqlite3": "2.1.6"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}}, "SQLitePCLRaw.core/2.1.6": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "build": {"buildTransitive/net8.0/SQLitePCLRaw.lib.e_sqlite3.targets": {}}, "runtimeTargets": {"runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a": {"assetType": "native", "rid": "browser-wasm"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-armel"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-mips64"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-ppc64le"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-s390x"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x86"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x86"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "compile": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}}, "System.CodeDom/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.CodeDom.dll": {}}}, "System.Collections.Immutable/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition/6.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.Convention/6.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "6.0.0"}, "compile": {"lib/net6.0/System.Composition.Convention.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.Convention.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.Hosting/6.0.0": {"type": "package", "dependencies": {"System.Composition.Runtime": "6.0.0"}, "compile": {"lib/net6.0/System.Composition.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.Runtime/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Composition.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.Runtime.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.TypedParts/6.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}, "compile": {"lib/net6.0/System.Composition.TypedParts.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.TypedParts.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.IO.Pipelines/6.0.3": {"type": "package", "compile": {"lib/net6.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Memory/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Reflection.Metadata/6.0.1": {"type": "package", "dependencies": {"System.Collections.Immutable": "6.0.0"}, "compile": {"lib/net6.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/8.0.4": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Threading.Channels/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Threading.Channels.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Threading.Channels.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}}, "net8.0-windows10.0.19041/win10-x64": {"CommunityToolkit.Mvvm/8.2.2": {"type": "package", "compile": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets": {}}}, "Humanizer.Core/2.14.1": {"type": "package", "compile": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Humanizer.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"type": "package", "build": {"build/_._": {}}}, "Microsoft.CodeAnalysis.Common/4.5.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3", "System.Collections.Immutable": "6.0.0", "System.Reflection.Metadata": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.5.0]"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "[4.5.0]", "Microsoft.CodeAnalysis.Common": "[4.5.0]", "Microsoft.CodeAnalysis.Workspaces.Common": "[4.5.0]"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "[4.5.0]", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3", "System.Threading.Channels": "6.0.0"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Data.Sqlite.Core/8.0.8": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "compile": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore/8.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.8", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.8", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.8": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.8": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Design/8.0.8": {"type": "package", "dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.8", "Microsoft.Extensions.DependencyModel": "8.0.1", "Mono.TextTemplating": "2.2.1"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"related": ".xml"}}, "build": {"build/net8.0/Microsoft.EntityFrameworkCore.Design.props": {}}}, "Microsoft.EntityFrameworkCore.Relational/8.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "8.0.8", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Sqlite/8.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "8.0.8", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.6"}, "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/8.0.8": {"type": "package", "dependencies": {"Microsoft.Data.Sqlite.Core": "8.0.8", "Microsoft.EntityFrameworkCore.Relational": "8.0.8", "Microsoft.Extensions.DependencyModel": "8.0.1"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Tools/8.0.8": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Design": "8.0.8"}, "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/8.0.1": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.4"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Graphics.Win2D/1.2.0": {"type": "package", "dependencies": {"Microsoft.WindowsAppSDK": "1.5.240227000"}, "compile": {"lib/net6.0-windows10.0.19041.0/Microsoft.Graphics.Canvas.Interop.dll": {}}, "runtime": {"lib/net6.0-windows10.0.19041.0/Microsoft.Graphics.Canvas.Interop.dll": {}}, "native": {"runtimes/win-x64/native/Microsoft.Graphics.Canvas.dll": {}}, "build": {"build/net6.0-windows10.0.19041.0/_._": {}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "compile": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"related": ".xml"}}}, "Microsoft.Maui.Controls/8.0.91": {"type": "package", "dependencies": {"Microsoft.Maui.Controls.Build.Tasks": "8.0.91", "Microsoft.Maui.Controls.Core": "8.0.91", "Microsoft.Maui.Controls.Xaml": "8.0.91", "Microsoft.Maui.Resizetizer": "8.0.91"}}, "Microsoft.Maui.Controls.Build.Tasks/8.0.91": {"type": "package", "dependencies": {"Microsoft.Maui.Controls.Core": "8.0.91", "Microsoft.Maui.Controls.Xaml": "8.0.91"}, "build": {"buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.props": {}, "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.targets": {}}}, "Microsoft.Maui.Controls.Compatibility/8.0.91": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Maui.Controls.Core": "8.0.91", "Microsoft.Maui.Controls.Xaml": "8.0.91"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.dll": {"related": ".pdb;.pri;.xml"}}}, "Microsoft.Maui.Controls.Core/8.0.91": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Maui.Core": "8.0.91"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.dll": {"related": ".pdb;.pri;.xml"}}, "resource": {"lib/net8.0-windows10.0.19041/ar/Microsoft.Maui.Controls.resources.dll": {"locale": "ar"}, "lib/net8.0-windows10.0.19041/ca/Microsoft.Maui.Controls.resources.dll": {"locale": "ca"}, "lib/net8.0-windows10.0.19041/cs/Microsoft.Maui.Controls.resources.dll": {"locale": "cs"}, "lib/net8.0-windows10.0.19041/da/Microsoft.Maui.Controls.resources.dll": {"locale": "da"}, "lib/net8.0-windows10.0.19041/de/Microsoft.Maui.Controls.resources.dll": {"locale": "de"}, "lib/net8.0-windows10.0.19041/el/Microsoft.Maui.Controls.resources.dll": {"locale": "el"}, "lib/net8.0-windows10.0.19041/es/Microsoft.Maui.Controls.resources.dll": {"locale": "es"}, "lib/net8.0-windows10.0.19041/fi/Microsoft.Maui.Controls.resources.dll": {"locale": "fi"}, "lib/net8.0-windows10.0.19041/fr/Microsoft.Maui.Controls.resources.dll": {"locale": "fr"}, "lib/net8.0-windows10.0.19041/he/Microsoft.Maui.Controls.resources.dll": {"locale": "he"}, "lib/net8.0-windows10.0.19041/hi/Microsoft.Maui.Controls.resources.dll": {"locale": "hi"}, "lib/net8.0-windows10.0.19041/hr/Microsoft.Maui.Controls.resources.dll": {"locale": "hr"}, "lib/net8.0-windows10.0.19041/hu/Microsoft.Maui.Controls.resources.dll": {"locale": "hu"}, "lib/net8.0-windows10.0.19041/id/Microsoft.Maui.Controls.resources.dll": {"locale": "id"}, "lib/net8.0-windows10.0.19041/it/Microsoft.Maui.Controls.resources.dll": {"locale": "it"}, "lib/net8.0-windows10.0.19041/ja/Microsoft.Maui.Controls.resources.dll": {"locale": "ja"}, "lib/net8.0-windows10.0.19041/ko/Microsoft.Maui.Controls.resources.dll": {"locale": "ko"}, "lib/net8.0-windows10.0.19041/ms/Microsoft.Maui.Controls.resources.dll": {"locale": "ms"}, "lib/net8.0-windows10.0.19041/nb/Microsoft.Maui.Controls.resources.dll": {"locale": "nb"}, "lib/net8.0-windows10.0.19041/nl/Microsoft.Maui.Controls.resources.dll": {"locale": "nl"}, "lib/net8.0-windows10.0.19041/pl/Microsoft.Maui.Controls.resources.dll": {"locale": "pl"}, "lib/net8.0-windows10.0.19041/pt-BR/Microsoft.Maui.Controls.resources.dll": {"locale": "pt-BR"}, "lib/net8.0-windows10.0.19041/pt/Microsoft.Maui.Controls.resources.dll": {"locale": "pt"}, "lib/net8.0-windows10.0.19041/ro/Microsoft.Maui.Controls.resources.dll": {"locale": "ro"}, "lib/net8.0-windows10.0.19041/ru/Microsoft.Maui.Controls.resources.dll": {"locale": "ru"}, "lib/net8.0-windows10.0.19041/sk/Microsoft.Maui.Controls.resources.dll": {"locale": "sk"}, "lib/net8.0-windows10.0.19041/sv/Microsoft.Maui.Controls.resources.dll": {"locale": "sv"}, "lib/net8.0-windows10.0.19041/th/Microsoft.Maui.Controls.resources.dll": {"locale": "th"}, "lib/net8.0-windows10.0.19041/tr/Microsoft.Maui.Controls.resources.dll": {"locale": "tr"}, "lib/net8.0-windows10.0.19041/uk/Microsoft.Maui.Controls.resources.dll": {"locale": "uk"}, "lib/net8.0-windows10.0.19041/vi/Microsoft.Maui.Controls.resources.dll": {"locale": "vi"}, "lib/net8.0-windows10.0.19041/zh-HK/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-HK"}, "lib/net8.0-windows10.0.19041/zh-Hans/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0-windows10.0.19041/zh-Hant/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Maui.Controls.Xaml/8.0.91": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Maui.Controls.Core": "8.0.91", "Microsoft.Maui.Core": "8.0.91"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Core/8.0.91": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.Maui.Essentials": "8.0.91", "Microsoft.Maui.Graphics": "8.0.91", "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop": "8.0.91", "Microsoft.Windows.SDK.BuildTools": "10.0.22621.756", "Microsoft.WindowsAppSDK": "1.5.240802000"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.dll": {"related": ".pdb;.pri;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.dll": {"related": ".pdb;.pri;.xml"}}, "build": {"buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.props": {}, "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.targets": {}}}, "Microsoft.Maui.Essentials/8.0.91": {"type": "package", "dependencies": {"Microsoft.Maui.Graphics": "8.0.91", "Microsoft.WindowsAppSDK": "1.5.240802000"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Essentials.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Essentials.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Graphics/8.0.91": {"type": "package", "dependencies": {"Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.1", "Microsoft.WindowsAppSDK": "1.5.240802000"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop/8.0.91": {"type": "package", "dependencies": {"Microsoft.Graphics.Win2D": "1.2.0", "Microsoft.Maui.Graphics": "8.0.91", "Microsoft.WindowsAppSDK": "1.5.240802000"}, "compile": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll": {"related": ".pdb"}}}, "Microsoft.Maui.Resizetizer/8.0.91": {"type": "package", "build": {"buildTransitive/Microsoft.Maui.Resizetizer.props": {}, "buildTransitive/Microsoft.Maui.Resizetizer.targets": {}}}, "Microsoft.Windows.SDK.BuildTools/10.0.22621.756": {"type": "package", "build": {"buildTransitive/Microsoft.Windows.SDK.BuildTools.props": {}, "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets": {}}}, "Microsoft.WindowsAppSDK/1.5.240802000": {"type": "package", "dependencies": {"Microsoft.Windows.SDK.BuildTools": "10.0.22621.756"}, "compile": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "runtime": {"lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll": {"related": ".xml"}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll": {}, "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll": {}}, "native": {"runtimes/win10-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll": {}}, "build": {"buildTransitive/Microsoft.WindowsAppSDK.props": {}, "buildTransitive/Microsoft.WindowsAppSDK.targets": {}}}, "Mono.TextTemplating/2.2.1": {"type": "package", "dependencies": {"System.CodeDom": "4.4.0"}, "compile": {"lib/netstandard2.0/Mono.TextTemplating.dll": {}}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"type": "package", "dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.6", "SQLitePCLRaw.provider.e_sqlite3": "2.1.6"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}}, "SQLitePCLRaw.core/2.1.6": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "native": {"runtimes/win-x64/native/e_sqlite3.dll": {}}, "build": {"buildTransitive/net8.0/SQLitePCLRaw.lib.e_sqlite3.targets": {}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "compile": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}}, "System.CodeDom/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.CodeDom.dll": {}}}, "System.Collections.Immutable/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition/6.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.AttributedModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.Convention/6.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "6.0.0"}, "compile": {"lib/net6.0/System.Composition.Convention.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.Convention.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.Hosting/6.0.0": {"type": "package", "dependencies": {"System.Composition.Runtime": "6.0.0"}, "compile": {"lib/net6.0/System.Composition.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.Runtime/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Composition.Runtime.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.Runtime.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Composition.TypedParts/6.0.0": {"type": "package", "dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}, "compile": {"lib/net6.0/System.Composition.TypedParts.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Composition.TypedParts.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.IO.Pipelines/6.0.3": {"type": "package", "compile": {"lib/net6.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Memory/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Reflection.Metadata/6.0.1": {"type": "package", "dependencies": {"System.Collections.Immutable": "6.0.0"}, "compile": {"lib/net6.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Text.Json/8.0.4": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Threading.Channels/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Threading.Channels.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Threading.Channels.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}}}, "libraries": {"CommunityToolkit.Mvvm/8.2.2": {"sha512": "r0g0k9tGYdrnz8R7T3x5UiokDffeevzK/2P/9SBL6fqLgN8B157MIi/bVUWI1KAz6ZorZrK9AdABCWUeXZZsvA==", "type": "package", "path": "communitytoolkit.mvvm/8.2.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/netstandard2.0/CommunityToolkit.Mvvm.targets", "build/netstandard2.1/CommunityToolkit.Mvvm.targets", "buildTransitive/netstandard2.0/CommunityToolkit.Mvvm.targets", "buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.2.2.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net6.0/CommunityToolkit.Mvvm.dll", "lib/net6.0/CommunityToolkit.Mvvm.pdb", "lib/net6.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "Humanizer.Core/2.14.1": {"sha512": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "type": "package", "path": "humanizer.core/2.14.1", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.2.14.1.nupkg.sha512", "humanizer.core.nuspec", "lib/net6.0/Humanizer.dll", "lib/net6.0/Humanizer.xml", "lib/netstandard1.0/Humanizer.dll", "lib/netstandard1.0/Humanizer.xml", "lib/netstandard2.0/Humanizer.dll", "lib/netstandard2.0/Humanizer.xml", "logo.png"]}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"sha512": "UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"sha512": "j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ==", "type": "package", "path": "microsoft.codeanalysis.analyzers/3.3.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "build/Microsoft.CodeAnalysis.Analyzers.props", "build/Microsoft.CodeAnalysis.Analyzers.targets", "build/config/analysislevel_2_9_8_all.editorconfig", "build/config/analysislevel_2_9_8_default.editorconfig", "build/config/analysislevel_2_9_8_minimum.editorconfig", "build/config/analysislevel_2_9_8_none.editorconfig", "build/config/analysislevel_2_9_8_recommended.editorconfig", "build/config/analysislevel_3_3_all.editorconfig", "build/config/analysislevel_3_3_default.editorconfig", "build/config/analysislevel_3_3_minimum.editorconfig", "build/config/analysislevel_3_3_none.editorconfig", "build/config/analysislevel_3_3_recommended.editorconfig", "build/config/analysislevel_3_all.editorconfig", "build/config/analysislevel_3_default.editorconfig", "build/config/analysislevel_3_minimum.editorconfig", "build/config/analysislevel_3_none.editorconfig", "build/config/analysislevel_3_recommended.editorconfig", "build/config/analysislevelcorrectness_2_9_8_all.editorconfig", "build/config/analysislevelcorrectness_2_9_8_default.editorconfig", "build/config/analysislevelcorrectness_2_9_8_minimum.editorconfig", "build/config/analysislevelcorrectness_2_9_8_none.editorconfig", "build/config/analysislevelcorrectness_2_9_8_recommended.editorconfig", "build/config/analysislevelcorrectness_3_3_all.editorconfig", "build/config/analysislevelcorrectness_3_3_default.editorconfig", "build/config/analysislevelcorrectness_3_3_minimum.editorconfig", "build/config/analysislevelcorrectness_3_3_none.editorconfig", "build/config/analysislevelcorrectness_3_3_recommended.editorconfig", "build/config/analysislevelcorrectness_3_all.editorconfig", "build/config/analysislevelcorrectness_3_default.editorconfig", "build/config/analysislevelcorrectness_3_minimum.editorconfig", "build/config/analysislevelcorrectness_3_none.editorconfig", "build/config/analysislevelcorrectness_3_recommended.editorconfig", "build/config/analysislevellibrary_2_9_8_all.editorconfig", "build/config/analysislevellibrary_2_9_8_default.editorconfig", "build/config/analysislevellibrary_2_9_8_minimum.editorconfig", "build/config/analysislevellibrary_2_9_8_none.editorconfig", "build/config/analysislevellibrary_2_9_8_recommended.editorconfig", "build/config/analysislevellibrary_3_3_all.editorconfig", "build/config/analysislevellibrary_3_3_default.editorconfig", "build/config/analysislevellibrary_3_3_minimum.editorconfig", "build/config/analysislevellibrary_3_3_none.editorconfig", "build/config/analysislevellibrary_3_3_recommended.editorconfig", "build/config/analysislevellibrary_3_all.editorconfig", "build/config/analysislevellibrary_3_default.editorconfig", "build/config/analysislevellibrary_3_minimum.editorconfig", "build/config/analysislevellibrary_3_none.editorconfig", "build/config/analysislevellibrary_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscompatibility_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysiscorrectness_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdesign_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisdocumentation_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysislocalization_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisperformance_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_2_9_8_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_3_recommended.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_all.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_default.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_minimum.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_none.editorconfig", "build/config/analysislevelmicrosoftcodeanalysisreleasetracking_3_recommended.editorconfig", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.Analyzers.md", "documentation/Microsoft.CodeAnalysis.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/CorrectnessRulesDefault/.editorconfig", "editorconfig/CorrectnessRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/LibraryRulesDefault/.editorconfig", "editorconfig/LibraryRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512", "microsoft.codeanalysis.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/CorrectnessRulesDefault.ruleset", "rulesets/CorrectnessRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/LibraryRulesDefault.ruleset", "rulesets/LibraryRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.Common/4.5.0": {"sha512": "lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==", "type": "package", "path": "microsoft.codeanalysis.common/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "microsoft.codeanalysis.common.4.5.0.nupkg.sha512", "microsoft.codeanalysis.common.nuspec"]}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"sha512": "cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==", "type": "package", "path": "microsoft.codeanalysis.csharp/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512", "microsoft.codeanalysis.csharp.nuspec"]}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"sha512": "h74wTpmGOp4yS4hj+EvNzEiPgg/KVs2wmSfTZ81upJZOtPkJsVkgfsgtxxqmAeapjT/vLKfmYV0bS8n5MNVP+g==", "type": "package", "path": "microsoft.codeanalysis.csharp.workspaces/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "microsoft.codeanalysis.csharp.workspaces.4.5.0.nupkg.sha512", "microsoft.codeanalysis.csharp.workspaces.nuspec"]}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"sha512": "l4dDRmGELXG72XZaonnOeORyD/T5RpEu5LGHOUIhnv+MmUWDY/m1kWXGwtcgQ5CJ5ynkFiRnIYzTKXYjUs7rbw==", "type": "package", "path": "microsoft.codeanalysis.workspaces.common/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.Workspaces.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.Workspaces.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "microsoft.codeanalysis.workspaces.common.4.5.0.nupkg.sha512", "microsoft.codeanalysis.workspaces.common.nuspec"]}, "Microsoft.Data.Sqlite.Core/8.0.8": {"sha512": "qHInO2EvOcPhjgboP0TGnXM7rASdvWXrw6jAH8Yuz5YP82VTje7d/NKiX1i+dVbE3+G3JuW1kqNVB8yLvsqgYA==", "type": "package", "path": "microsoft.data.sqlite.core/8.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net6.0/Microsoft.Data.Sqlite.dll", "lib/net6.0/Microsoft.Data.Sqlite.xml", "lib/net8.0/Microsoft.Data.Sqlite.dll", "lib/net8.0/Microsoft.Data.Sqlite.xml", "lib/netstandard2.0/Microsoft.Data.Sqlite.dll", "lib/netstandard2.0/Microsoft.Data.Sqlite.xml", "microsoft.data.sqlite.core.8.0.8.nupkg.sha512", "microsoft.data.sqlite.core.nuspec"]}, "Microsoft.EntityFrameworkCore/8.0.8": {"sha512": "iK+jrJzkfbIxutB7or808BPmJtjUEi5O+eSM7cLDwsyde6+3iOujCSfWnrHrLxY3u+EQrJD+aD8DJ6ogPA2Rtw==", "type": "package", "path": "microsoft.entityframeworkcore/8.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props", "lib/net8.0/Microsoft.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.8.0.8.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.8": {"sha512": "9mMQkZsfL1c2iifBD8MWRmwy59rvsVtR9NOezJj7+g1j4P7g49MJHd8k8faC/v7d5KuHkQ6KOQiSItvoRt9PXA==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/8.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.8.0.8.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.8": {"sha512": "OlAXMU+VQgLz5y5/SBkLvAa9VeiR3dlJqgIebEEH2M2NGA3evm68/Tv7SLWmSxwnEAtA3nmDEZF2pacK6eXh4Q==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/8.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "docs/PACKAGE.md", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.8.0.8.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Design/8.0.8": {"sha512": "MmQAMHdjZR8Iyn/FVQrh9weJQTn0HqtKa3vELS9ffQJat/qXgnTam9M9jqvePphjkYp5Scee+Hy+EJR4nmWmOA==", "type": "package", "path": "microsoft.entityframeworkcore.design/8.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "build/net8.0/Microsoft.EntityFrameworkCore.Design.props", "lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Design.xml", "microsoft.entityframeworkcore.design.8.0.8.nupkg.sha512", "microsoft.entityframeworkcore.design.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/8.0.8": {"sha512": "3WnrwdXxKg4L98cDx0lNEEau8U2lsfuBJCs0Yzht+5XVTmahboM7MukKfQHAzVsHUPszm6ci929S7Qas0WfVHA==", "type": "package", "path": "microsoft.entityframeworkcore.relational/8.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.8.0.8.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite/8.0.8": {"sha512": "IDB7Xs16hN/3VkWFCCa4r3fqoJxMVezwq418gr8dBkRBO0pxH+BX/Kjk/U3PYXDvzVLkXqUgJsHv1XoFrJbZPQ==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite/8.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/_._", "microsoft.entityframeworkcore.sqlite.8.0.8.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite.Core/8.0.8": {"sha512": "w5k/ENj3+BPbmggqh83RRuPhhKcJmW7CmdJuGwdX1eFrmptJwnzKiHfQCPkJAu9df16PSs5YFeWrDgepfqnltA==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite.core/8.0.8", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.xml", "microsoft.entityframeworkcore.sqlite.core.8.0.8.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.core.nuspec"]}, "Microsoft.EntityFrameworkCore.Tools/8.0.8": {"sha512": "wjDNbLJk86QpZt2JxJuNVzpBKIbEQsgcJYHGeIFTBuK6NEgvJvyxgneg059HfSJmTVdInZ61lTO4sJGCfFr7+w==", "type": "package", "path": "microsoft.entityframeworkcore.tools/8.0.8", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "docs/PACKAGE.md", "lib/net8.0/_._", "microsoft.entityframeworkcore.tools.8.0.8.nupkg.sha512", "microsoft.entityframeworkcore.tools.nuspec", "tools/EntityFrameworkCore.PS2.psd1", "tools/EntityFrameworkCore.PS2.psm1", "tools/EntityFrameworkCore.psd1", "tools/EntityFrameworkCore.psm1", "tools/about_EntityFrameworkCore.help.txt", "tools/init.ps1", "tools/net461/any/ef.exe", "tools/net461/win-arm64/ef.exe", "tools/net461/win-x86/ef.exe", "tools/netcoreapp2.0/any/ef.dll", "tools/netcoreapp2.0/any/ef.runtimeconfig.json"]}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"sha512": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "type": "package", "path": "microsoft.extensions.caching.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"sha512": "7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "type": "package", "path": "microsoft.extensions.caching.memory/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/8.0.0": {"sha512": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "type": "package", "path": "microsoft.extensions.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"sha512": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"sha512": "cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/8.0.1": {"sha512": "5Ou6varcxLBzQ+Agfm0k0pnH7vrEITYlXMDuE6s7ZHlZHz6/G8XJ3iISZDr5rfwfge6RnXJ1+Wc479mMn52vjA==", "type": "package", "path": "microsoft.extensions.dependencymodel/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net6.0/Microsoft.Extensions.DependencyModel.dll", "lib/net6.0/Microsoft.Extensions.DependencyModel.xml", "lib/net7.0/Microsoft.Extensions.DependencyModel.dll", "lib/net7.0/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.8.0.1.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/8.0.0": {"sha512": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "type": "package", "path": "microsoft.extensions.logging/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.8.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"sha512": "arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"sha512": "dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "type": "package", "path": "microsoft.extensions.logging.debug/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net6.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net6.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net7.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net7.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.8.0.0.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/8.0.0": {"sha512": "JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "type": "package", "path": "microsoft.extensions.options/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Graphics.Win2D/1.2.0": {"sha512": "7bAo8ObjCy/br0eW0nONRfVKehJu5aDe/KQekWeNXslwTOO2rhrIfWaVGepsXyVqmqwHoLJ31g1HsT7FLdBCoQ==", "type": "package", "path": "microsoft.graphics.win2d/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Win2d.githash.txt", "build/Win2D.common.targets", "build/native/Microsoft.Graphics.Win2D.targets", "build/net45/Microsoft.Graphics.Win2D.targets", "build/net6.0-windows10.0.19041.0/Microsoft.Graphics.Win2D.targets", "build/win10/Microsoft.Graphics.Win2D.targets", "icon.png", "include/Microsoft.Graphics.Canvas.native.h", "include/arm64/Microsoft.Graphics.Canvas.h", "include/x64/Microsoft.Graphics.Canvas.h", "include/x86/Microsoft.Graphics.Canvas.h", "lib/net45/Microsoft.Graphics.Canvas.winmd", "lib/net6.0-windows10.0.19041.0/Microsoft.Graphics.Canvas.Interop.dll", "lib/uap10.0/Microsoft.Graphics.Canvas.winmd", "microsoft.graphics.win2d.1.2.0.nupkg.sha512", "microsoft.graphics.win2d.nuspec", "runtimes/win-arm64/native/Microsoft.Graphics.Canvas.dll", "runtimes/win-x64/native/Microsoft.Graphics.Canvas.dll", "runtimes/win-x86/native/Microsoft.Graphics.Canvas.dll"]}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"sha512": "s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "type": "package", "path": "microsoft.io.recyclablememorystream/3.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/net6.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.0/Microsoft.IO.RecyclableMemoryStream.xml", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.dll", "lib/netstandard2.1/Microsoft.IO.RecyclableMemoryStream.xml", "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512", "microsoft.io.recyclablememorystream.nuspec"]}, "Microsoft.Maui.Controls/8.0.91": {"sha512": "gqyrGqSQfYF2SMOS/jpWTBK7KVNsm8p4LvREATKXgEYuLN6ivrOXC+E4TrZaILEiVXB4pQr+kbb7ewzAVjSbeg==", "type": "package", "path": "microsoft.maui.controls/8.0.91", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "microsoft.maui.controls.8.0.91.nupkg.sha512", "microsoft.maui.controls.nuspec"]}, "Microsoft.Maui.Controls.Build.Tasks/8.0.91": {"sha512": "is+MHVT02RSJxhBSAx1Z7xTXnalADGpsS2TDUCsgOEjsWrpzDoYwvqk9Yh/eHnvmDHzenMeWAvK7CLmAlaIlIQ==", "type": "package", "path": "microsoft.maui.controls.build.tasks/8.0.91", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/net6.0-ios10.0/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/net6.0-ios10.0/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/net6.0-ios10.0/Microsoft.Maui.Controls.iOS.targets", "buildTransitive/net6.0-maccatalyst13.1/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/net6.0-maccatalyst13.1/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/net6.0-maccatalyst13.1/Microsoft.Maui.Controls.MacCatalyst.targets", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Sdk.Windows.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.After.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.Before.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.dll", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.pdb", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Common.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.DefaultItems.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Globs.props", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.SingleProject.Before.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.SingleProject.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.SourceGen.dll", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.SourceGen.pdb", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.targets", "buildTransitive/netstandard2.0/Mono.Cecil.Mdb.dll", "buildTransitive/netstandard2.0/Mono.Cecil.Mdb.pdb", "buildTransitive/netstandard2.0/Mono.Cecil.Pdb.dll", "buildTransitive/netstandard2.0/Mono.Cecil.Pdb.pdb", "buildTransitive/netstandard2.0/Mono.Cecil.Rocks.dll", "buildTransitive/netstandard2.0/Mono.Cecil.Rocks.pdb", "buildTransitive/netstandard2.0/Mono.Cecil.dll", "buildTransitive/netstandard2.0/Mono.Cecil.pdb", "buildTransitive/netstandard2.0/System.CodeDom.dll", "buildTransitive/netstandard2.0/ar/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ca/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/cs/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/da/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/de/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/el/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/es/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/fi/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/fr/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/he/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/hi/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/hr/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/hu/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/id/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/it/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ja/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ko/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/maui-blazor.aotprofile", "buildTransitive/netstandard2.0/maui.aotprofile", "buildTransitive/netstandard2.0/ms/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/nb/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/nl/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/pl/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/proguard.cfg", "buildTransitive/netstandard2.0/pt-BR/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/pt/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ro/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ru/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/sk/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/sv/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/th/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/tr/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/uk/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/vi/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/zh-HK/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/zh-Hans/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/zh-Hant/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "microsoft.maui.controls.build.tasks.8.0.91.nupkg.sha512", "microsoft.maui.controls.build.tasks.nuspec"]}, "Microsoft.Maui.Controls.Compatibility/8.0.91": {"sha512": "Cpmplu8tnIGg20d7V5fxTL2lhBDe3oBV1NV10rf6jXZptA/LJh2lrNsLM9/3X/1vKTL+oEnhoVNgXFPFiNOOTg==", "type": "package", "path": "microsoft.maui.controls.compatibility/8.0.91", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0-android34.0/Microsoft.Maui.Controls.Compatibility.aar", "lib/net8.0-android34.0/Microsoft.Maui.Controls.Compatibility.dll", "lib/net8.0-android34.0/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net8.0-android34.0/Microsoft.Maui.Controls.Compatibility.xml", "lib/net8.0-ios17.0/Microsoft.Maui.Controls.Compatibility.dll", "lib/net8.0-ios17.0/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net8.0-ios17.0/Microsoft.Maui.Controls.Compatibility.xml", "lib/net8.0-ios17.5/Microsoft.Maui.Controls.Compatibility.dll", "lib/net8.0-ios17.5/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net8.0-ios17.5/Microsoft.Maui.Controls.Compatibility.xml", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Controls.Compatibility.dll", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Controls.Compatibility.xml", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Controls.Compatibility.dll", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Controls.Compatibility.xml", "lib/net8.0-tizen7.0/Microsoft.Maui.Controls.Compatibility.dll", "lib/net8.0-tizen7.0/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net8.0-tizen7.0/Microsoft.Maui.Controls.Compatibility.xml", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.dll", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.pri", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Compatibility.xml", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Controls.Compatibility.dll", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Controls.Compatibility.pri", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Controls.Compatibility.xml", "lib/net8.0/Microsoft.Maui.Controls.Compatibility.dll", "lib/net8.0/Microsoft.Maui.Controls.Compatibility.pdb", "lib/net8.0/Microsoft.Maui.Controls.Compatibility.xml", "lib/netstandard2.0/Microsoft.Maui.Controls.Compatibility.dll", "lib/netstandard2.0/Microsoft.Maui.Controls.Compatibility.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.Compatibility.xml", "lib/netstandard2.1/Microsoft.Maui.Controls.Compatibility.dll", "lib/netstandard2.1/Microsoft.Maui.Controls.Compatibility.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.Compatibility.xml", "microsoft.maui.controls.compatibility.8.0.91.nupkg.sha512", "microsoft.maui.controls.compatibility.nuspec"]}, "Microsoft.Maui.Controls.Core/8.0.91": {"sha512": "9dEmHV7FK9PdqUkZJw9nQIwYuN9Itk27+DiiH7ZcH5DhKM4VtaA8RzkOkHgdbOplGg0/OnFSK2c3agHWxDWAAw==", "type": "package", "path": "microsoft.maui.controls.core/8.0.91", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0-android34.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net8.0-android34.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net8.0-android34.0/Microsoft.Maui.Controls.aar", "lib/net8.0-android34.0/Microsoft.Maui.Controls.dll", "lib/net8.0-android34.0/Microsoft.Maui.Controls.pdb", "lib/net8.0-android34.0/Microsoft.Maui.Controls.xml", "lib/net8.0-android34.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-android34.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net8.0-ios17.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net8.0-ios17.0/Microsoft.Maui.Controls.dll", "lib/net8.0-ios17.0/Microsoft.Maui.Controls.pdb", "lib/net8.0-ios17.0/Microsoft.Maui.Controls.xml", "lib/net8.0-ios17.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net8.0-ios17.5/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net8.0-ios17.5/Microsoft.Maui.Controls.dll", "lib/net8.0-ios17.5/Microsoft.Maui.Controls.pdb", "lib/net8.0-ios17.5/Microsoft.Maui.Controls.xml", "lib/net8.0-ios17.5/ar/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/ca/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/cs/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/da/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/de/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/el/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/es/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/fi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/fr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/he/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/hi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/hr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/hu/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/id/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/it/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/ja/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/ko/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/ms/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/nb/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/nl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/pl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/pt/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/ro/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/ru/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/sk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/sv/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/th/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/tr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/uk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/vi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-ios17.5/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net8.0-maccatalyst17.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Controls.dll", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Controls.pdb", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Controls.xml", "lib/net8.0-maccatalyst17.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net8.0-maccatalyst17.5/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Controls.dll", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Controls.pdb", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Controls.xml", "lib/net8.0-maccatalyst17.5/ar/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/ca/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/cs/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/da/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/de/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/el/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/es/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/fi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/fr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/he/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/hi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/hr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/hu/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/id/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/it/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/ja/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/ko/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/ms/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/nb/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/nl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/pl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/pt/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/ro/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/ru/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/sk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/sv/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/th/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/tr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/uk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/vi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-maccatalyst17.5/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net8.0-tizen7.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net8.0-tizen7.0/Microsoft.Maui.Controls.dll", "lib/net8.0-tizen7.0/Microsoft.Maui.Controls.pdb", "lib/net8.0-tizen7.0/Microsoft.Maui.Controls.xml", "lib/net8.0-tizen7.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-tizen7.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net8.0-windows10.0.19041/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.dll", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.pdb", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.pri", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.xml", "lib/net8.0-windows10.0.19041/ar/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/ca/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/cs/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/da/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/de/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/el/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/es/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/fi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/fr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/he/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/hi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/hr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/hu/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/id/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/it/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/ja/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/ko/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/ms/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/nb/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/nl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/pl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/pt/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/ro/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/ru/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/sk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/sv/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/th/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/tr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/uk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/vi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.19041/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net8.0-windows10.0.20348/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Controls.dll", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Controls.pdb", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Controls.pri", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Controls.xml", "lib/net8.0-windows10.0.20348/ar/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/ca/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/cs/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/da/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/de/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/el/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/es/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/fi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/fr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/he/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/hi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/hr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/hu/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/id/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/it/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/ja/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/ko/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/ms/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/nb/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/nl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/pl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/pt/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/ro/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/ru/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/sk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/sv/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/th/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/tr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/uk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/vi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/zh-<PERSON>/Microsoft.Maui.Controls.resources.dll", "lib/net8.0-windows10.0.20348/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net8.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net8.0/Microsoft.Maui.Controls.dll", "lib/net8.0/Microsoft.Maui.Controls.pdb", "lib/net8.0/Microsoft.Maui.Controls.xml", "lib/net8.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/netstandard2.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.dll", "lib/netstandard2.0/Microsoft.Maui.Controls.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.xml", "lib/netstandard2.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/da/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/de/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/el/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/es/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/he/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/id/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/it/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/th/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/netstandard2.1/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.dll", "lib/netstandard2.1/Microsoft.Maui.Controls.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.xml", "lib/netstandard2.1/ar/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ca/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/cs/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/da/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/de/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/el/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/es/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/fi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/fr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/he/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/hi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/hr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/hu/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/id/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/it/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ja/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ko/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ms/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/nb/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/nl/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/pl/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/pt/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ro/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ru/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/sk/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/sv/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/th/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/tr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/uk/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/vi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/zh-<PERSON>/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/zh-Hant/Microsoft.Maui.Controls.resources.dll", "microsoft.maui.controls.core.8.0.91.nupkg.sha512", "microsoft.maui.controls.core.nuspec"]}, "Microsoft.Maui.Controls.Xaml/8.0.91": {"sha512": "mTz5EGfhpkv3KPHYBPeCm6PWHN+xWAMsH1KCPM/KQfTY53tWVW6Ocp8gnty0A9a3JQ1QISHSxjcnesKV+RPd+w==", "type": "package", "path": "microsoft.maui.controls.xaml/8.0.91", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0-android34.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net8.0-android34.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net8.0-android34.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net8.0-android34.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net8.0-android34.0/Microsoft.Maui.Controls.Xaml.xml", "lib/net8.0-ios17.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net8.0-ios17.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net8.0-ios17.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net8.0-ios17.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net8.0-ios17.0/Microsoft.Maui.Controls.Xaml.xml", "lib/net8.0-ios17.5/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net8.0-ios17.5/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net8.0-ios17.5/Microsoft.Maui.Controls.Xaml.dll", "lib/net8.0-ios17.5/Microsoft.Maui.Controls.Xaml.pdb", "lib/net8.0-ios17.5/Microsoft.Maui.Controls.Xaml.xml", "lib/net8.0-maccatalyst17.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net8.0-maccatalyst17.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Controls.Xaml.xml", "lib/net8.0-maccatalyst17.5/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net8.0-maccatalyst17.5/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Controls.Xaml.dll", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Controls.Xaml.pdb", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Controls.Xaml.xml", "lib/net8.0-tizen7.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net8.0-tizen7.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net8.0-tizen7.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net8.0-tizen7.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net8.0-tizen7.0/Microsoft.Maui.Controls.Xaml.xml", "lib/net8.0-windows10.0.19041/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net8.0-windows10.0.19041/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.dll", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.pdb", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.xml", "lib/net8.0-windows10.0.20348/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net8.0-windows10.0.20348/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Controls.Xaml.dll", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Controls.Xaml.pdb", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Controls.Xaml.xml", "lib/net8.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net8.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net8.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net8.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net8.0/Microsoft.Maui.Controls.Xaml.xml", "lib/netstandard2.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/netstandard2.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.Xaml.dll", "lib/netstandard2.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.Xaml.xml", "lib/netstandard2.1/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/netstandard2.1/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.Xaml.dll", "lib/netstandard2.1/Microsoft.Maui.Controls.Xaml.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.Xaml.xml", "microsoft.maui.controls.xaml.8.0.91.nupkg.sha512", "microsoft.maui.controls.xaml.nuspec"]}, "Microsoft.Maui.Core/8.0.91": {"sha512": "582OKTQ6ZyM47EXyNd1P6qL+W9zfOyUQbqJOAsHi6gpgiXJiRRB+/PcnFxkxDybKFz/LC0SwndOpK5DchSjVjg==", "type": "package", "path": "microsoft.maui.core/8.0.91", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/Microsoft.Maui.Core.After.targets", "buildTransitive/Microsoft.Maui.Core.Before.targets", "buildTransitive/Microsoft.Maui.Core.BundledVersions.targets", "buildTransitive/Microsoft.Maui.Core.props", "buildTransitive/Microsoft.Maui.Core.targets", "buildTransitive/WinUI.targets", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.props", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.targets", "lib/net8.0-android34.0/Microsoft.Maui.aar", "lib/net8.0-android34.0/Microsoft.Maui.dll", "lib/net8.0-android34.0/Microsoft.Maui.pdb", "lib/net8.0-android34.0/Microsoft.Maui.xml", "lib/net8.0-android34.0/maui.aar", "lib/net8.0-ios17.0/Microsoft.Maui.dll", "lib/net8.0-ios17.0/Microsoft.Maui.pdb", "lib/net8.0-ios17.0/Microsoft.Maui.xml", "lib/net8.0-ios17.5/Microsoft.Maui.dll", "lib/net8.0-ios17.5/Microsoft.Maui.pdb", "lib/net8.0-ios17.5/Microsoft.Maui.xml", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.dll", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.pdb", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.xml", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.dll", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.pdb", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.xml", "lib/net8.0-tizen7.0/Microsoft.Maui.dll", "lib/net8.0-tizen7.0/Microsoft.Maui.pdb", "lib/net8.0-tizen7.0/Microsoft.Maui.xml", "lib/net8.0-windows10.0.19041/Microsoft.Maui.dll", "lib/net8.0-windows10.0.19041/Microsoft.Maui.pdb", "lib/net8.0-windows10.0.19041/Microsoft.Maui.pri", "lib/net8.0-windows10.0.19041/Microsoft.Maui.xml", "lib/net8.0-windows10.0.20348/Microsoft.Maui.dll", "lib/net8.0-windows10.0.20348/Microsoft.Maui.pdb", "lib/net8.0-windows10.0.20348/Microsoft.Maui.pri", "lib/net8.0-windows10.0.20348/Microsoft.Maui.xml", "lib/net8.0/Microsoft.Maui.dll", "lib/net8.0/Microsoft.Maui.pdb", "lib/net8.0/Microsoft.Maui.xml", "lib/netstandard2.0/Microsoft.Maui.dll", "lib/netstandard2.0/Microsoft.Maui.pdb", "lib/netstandard2.0/Microsoft.Maui.xml", "lib/netstandard2.1/Microsoft.Maui.dll", "lib/netstandard2.1/Microsoft.Maui.pdb", "lib/netstandard2.1/Microsoft.Maui.xml", "microsoft.maui.core.8.0.91.nupkg.sha512", "microsoft.maui.core.nuspec"]}, "Microsoft.Maui.Essentials/8.0.91": {"sha512": "oLpAGwIsHXvy0iTXbeVVrFNFpJaPoTceoP/KgigrEal3PTHPfFqVgcayhsvzVMw+oMU0vDpcxjbbpBlSLG+Fiw==", "type": "package", "path": "microsoft.maui.essentials/8.0.91", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net7.0-android33.0/Microsoft.Maui.Essentials.aar", "lib/net7.0-android33.0/Microsoft.Maui.Essentials.dll", "lib/net7.0-android33.0/Microsoft.Maui.Essentials.pdb", "lib/net7.0-android33.0/Microsoft.Maui.Essentials.xml", "lib/net7.0-ios16.1/Microsoft.Maui.Essentials.dll", "lib/net7.0-ios16.1/Microsoft.Maui.Essentials.pdb", "lib/net7.0-ios16.1/Microsoft.Maui.Essentials.xml", "lib/net7.0-maccatalyst16.1/Microsoft.Maui.Essentials.dll", "lib/net7.0-maccatalyst16.1/Microsoft.Maui.Essentials.pdb", "lib/net7.0-maccatalyst16.1/Microsoft.Maui.Essentials.xml", "lib/net7.0-tizen7.0/Microsoft.Maui.Essentials.dll", "lib/net7.0-tizen7.0/Microsoft.Maui.Essentials.pdb", "lib/net7.0-tizen7.0/Microsoft.Maui.Essentials.xml", "lib/net7.0-windows10.0.19041/Microsoft.Maui.Essentials.dll", "lib/net7.0-windows10.0.19041/Microsoft.Maui.Essentials.pdb", "lib/net7.0-windows10.0.19041/Microsoft.Maui.Essentials.xml", "lib/net7.0-windows10.0.20348/Microsoft.Maui.Essentials.dll", "lib/net7.0-windows10.0.20348/Microsoft.Maui.Essentials.pdb", "lib/net7.0-windows10.0.20348/Microsoft.Maui.Essentials.xml", "lib/net7.0/Microsoft.Maui.Essentials.dll", "lib/net7.0/Microsoft.Maui.Essentials.pdb", "lib/net7.0/Microsoft.Maui.Essentials.xml", "lib/net8.0-android34.0/Microsoft.Maui.Essentials.aar", "lib/net8.0-android34.0/Microsoft.Maui.Essentials.dll", "lib/net8.0-android34.0/Microsoft.Maui.Essentials.pdb", "lib/net8.0-android34.0/Microsoft.Maui.Essentials.xml", "lib/net8.0-ios17.0/Microsoft.Maui.Essentials.dll", "lib/net8.0-ios17.0/Microsoft.Maui.Essentials.pdb", "lib/net8.0-ios17.0/Microsoft.Maui.Essentials.xml", "lib/net8.0-ios17.5/Microsoft.Maui.Essentials.dll", "lib/net8.0-ios17.5/Microsoft.Maui.Essentials.pdb", "lib/net8.0-ios17.5/Microsoft.Maui.Essentials.xml", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Essentials.dll", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Essentials.pdb", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Essentials.xml", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Essentials.dll", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Essentials.pdb", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Essentials.xml", "lib/net8.0-tizen7.0/Microsoft.Maui.Essentials.dll", "lib/net8.0-tizen7.0/Microsoft.Maui.Essentials.pdb", "lib/net8.0-tizen7.0/Microsoft.Maui.Essentials.xml", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Essentials.dll", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Essentials.pdb", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Essentials.xml", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Essentials.dll", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Essentials.pdb", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Essentials.xml", "lib/net8.0/Microsoft.Maui.Essentials.dll", "lib/net8.0/Microsoft.Maui.Essentials.pdb", "lib/net8.0/Microsoft.Maui.Essentials.xml", "lib/netstandard2.0/Microsoft.Maui.Essentials.dll", "lib/netstandard2.0/Microsoft.Maui.Essentials.pdb", "lib/netstandard2.0/Microsoft.Maui.Essentials.xml", "lib/netstandard2.1/Microsoft.Maui.Essentials.dll", "lib/netstandard2.1/Microsoft.Maui.Essentials.pdb", "lib/netstandard2.1/Microsoft.Maui.Essentials.xml", "microsoft.maui.essentials.8.0.91.nupkg.sha512", "microsoft.maui.essentials.nuspec"]}, "Microsoft.Maui.Graphics/8.0.91": {"sha512": "p/LGI3YOcFjnBJaiKZFX0BwtWzh2RPVRQpfAT6Fd+hNViCtM8zJjIMmmFWurJnKK1wytC5AHRdZpfdELhrmugw==", "type": "package", "path": "microsoft.maui.graphics/8.0.91", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net7.0-android33.0/Microsoft.Maui.Graphics.dll", "lib/net7.0-android33.0/Microsoft.Maui.Graphics.pdb", "lib/net7.0-android33.0/Microsoft.Maui.Graphics.xml", "lib/net7.0-ios16.1/Microsoft.Maui.Graphics.dll", "lib/net7.0-ios16.1/Microsoft.Maui.Graphics.pdb", "lib/net7.0-ios16.1/Microsoft.Maui.Graphics.xml", "lib/net7.0-maccatalyst16.1/Microsoft.Maui.Graphics.dll", "lib/net7.0-maccatalyst16.1/Microsoft.Maui.Graphics.pdb", "lib/net7.0-maccatalyst16.1/Microsoft.Maui.Graphics.xml", "lib/net7.0-macos13.0/Microsoft.Maui.Graphics.dll", "lib/net7.0-macos13.0/Microsoft.Maui.Graphics.pdb", "lib/net7.0-macos13.0/Microsoft.Maui.Graphics.xml", "lib/net7.0-tizen7.0/Microsoft.Maui.Graphics.dll", "lib/net7.0-tizen7.0/Microsoft.Maui.Graphics.pdb", "lib/net7.0-tizen7.0/Microsoft.Maui.Graphics.xml", "lib/net7.0-windows10.0.19041/Microsoft.Maui.Graphics.dll", "lib/net7.0-windows10.0.19041/Microsoft.Maui.Graphics.pdb", "lib/net7.0-windows10.0.19041/Microsoft.Maui.Graphics.xml", "lib/net7.0-windows10.0.20348/Microsoft.Maui.Graphics.dll", "lib/net7.0-windows10.0.20348/Microsoft.Maui.Graphics.pdb", "lib/net7.0-windows10.0.20348/Microsoft.Maui.Graphics.xml", "lib/net7.0/Microsoft.Maui.Graphics.dll", "lib/net7.0/Microsoft.Maui.Graphics.pdb", "lib/net7.0/Microsoft.Maui.Graphics.xml", "lib/net8.0-android34.0/Microsoft.Maui.Graphics.dll", "lib/net8.0-android34.0/Microsoft.Maui.Graphics.pdb", "lib/net8.0-android34.0/Microsoft.Maui.Graphics.xml", "lib/net8.0-ios17.0/Microsoft.Maui.Graphics.dll", "lib/net8.0-ios17.0/Microsoft.Maui.Graphics.pdb", "lib/net8.0-ios17.0/Microsoft.Maui.Graphics.xml", "lib/net8.0-ios17.5/Microsoft.Maui.Graphics.dll", "lib/net8.0-ios17.5/Microsoft.Maui.Graphics.pdb", "lib/net8.0-ios17.5/Microsoft.Maui.Graphics.xml", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Graphics.dll", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Graphics.pdb", "lib/net8.0-maccatalyst17.0/Microsoft.Maui.Graphics.xml", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Graphics.dll", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Graphics.pdb", "lib/net8.0-maccatalyst17.5/Microsoft.Maui.Graphics.xml", "lib/net8.0-macos14.0/Microsoft.Maui.Graphics.dll", "lib/net8.0-macos14.0/Microsoft.Maui.Graphics.pdb", "lib/net8.0-macos14.0/Microsoft.Maui.Graphics.xml", "lib/net8.0-tizen7.0/Microsoft.Maui.Graphics.dll", "lib/net8.0-tizen7.0/Microsoft.Maui.Graphics.pdb", "lib/net8.0-tizen7.0/Microsoft.Maui.Graphics.xml", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.dll", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.pdb", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.xml", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Graphics.dll", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Graphics.pdb", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Graphics.xml", "lib/net8.0/Microsoft.Maui.Graphics.dll", "lib/net8.0/Microsoft.Maui.Graphics.pdb", "lib/net8.0/Microsoft.Maui.Graphics.xml", "lib/netstandard2.0/Microsoft.Maui.Graphics.dll", "lib/netstandard2.0/Microsoft.Maui.Graphics.pdb", "lib/netstandard2.0/Microsoft.Maui.Graphics.xml", "lib/netstandard2.1/Microsoft.Maui.Graphics.dll", "lib/netstandard2.1/Microsoft.Maui.Graphics.pdb", "lib/netstandard2.1/Microsoft.Maui.Graphics.xml", "microsoft.maui.graphics.8.0.91.nupkg.sha512", "microsoft.maui.graphics.nuspec"]}, "Microsoft.Maui.Graphics.Win2D.WinUI.Desktop/8.0.91": {"sha512": "jKrQmQR9daoAPLYJKjI6bt1S1mtsMRhWbhJomhvMEZFwfxv+p7YHYCF16hhEaa0Z/liQiIXM4xW670mF6gvI9Q==", "type": "package", "path": "microsoft.maui.graphics.win2d.winui.desktop/8.0.91", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net7.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll", "lib/net7.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.pdb", "lib/net7.0-windows10.0.20348/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll", "lib/net7.0-windows10.0.20348/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.pdb", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll", "lib/net8.0-windows10.0.19041/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.pdb", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll", "lib/net8.0-windows10.0.20348/Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.pdb", "microsoft.maui.graphics.win2d.winui.desktop.8.0.91.nupkg.sha512", "microsoft.maui.graphics.win2d.winui.desktop.nuspec"]}, "Microsoft.Maui.Resizetizer/8.0.91": {"sha512": "LJF4SsXD4CYk5/W+LxqSHy9CnteHAHuINTKdo2P0xdceZP2J4UZax5ZVPg9KVWBMlL1Gmfqq/sdrkbckgufkmA==", "type": "package", "path": "microsoft.maui.resizetizer/8.0.91", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/ExCSS.dll", "buildTransitive/Fizzler.dll", "buildTransitive/HarfBuzzSharp.dll", "buildTransitive/HarfBuzzSharp.pdb", "buildTransitive/Microsoft.Bcl.AsyncInterfaces.dll", "buildTransitive/Microsoft.Maui.Resizetizer.After.targets", "buildTransitive/Microsoft.Maui.Resizetizer.Before.targets", "buildTransitive/Microsoft.Maui.Resizetizer.dll", "buildTransitive/Microsoft.Maui.Resizetizer.pdb", "buildTransitive/Microsoft.Maui.Resizetizer.props", "buildTransitive/Microsoft.Maui.Resizetizer.targets", "buildTransitive/ShimSkiaSharp.dll", "buildTransitive/SkiaSharp.HarfBuzz.dll", "buildTransitive/SkiaSharp.HarfBuzz.pdb", "buildTransitive/SkiaSharp.dll", "buildTransitive/SkiaSharp.pdb", "buildTransitive/Svg.Custom.dll", "buildTransitive/Svg.Model.dll", "buildTransitive/Svg.Skia.dll", "buildTransitive/System.Buffers.dll", "buildTransitive/System.IO.UnmanagedMemoryStream.dll", "buildTransitive/System.Memory.dll", "buildTransitive/System.Numerics.Vectors.dll", "buildTransitive/System.ObjectModel.dll", "buildTransitive/System.Runtime.CompilerServices.Unsafe.dll", "buildTransitive/System.Text.Encodings.Web.dll", "buildTransitive/System.Text.Json.dll", "buildTransitive/arm/libHarfBuzzSharp.so", "buildTransitive/arm/libSkiaSharp.so", "buildTransitive/arm64/libHarfBuzzSharp.dll", "buildTransitive/arm64/libHarfBuzzSharp.so", "buildTransitive/arm64/libSkiaSharp.dll", "buildTransitive/arm64/libSkiaSharp.so", "buildTransitive/libHarfBuzzSharp.dylib", "buildTransitive/libSkiaSharp.dylib", "buildTransitive/musl-x64/libHarfBuzzSharp.so", "buildTransitive/musl-x64/libSkiaSharp.so", "buildTransitive/x64/libHarfBuzzSharp.dll", "buildTransitive/x64/libHarfBuzzSharp.so", "buildTransitive/x64/libSkiaSharp.dll", "buildTransitive/x64/libSkiaSharp.so", "buildTransitive/x86/libHarfBuzzSharp.dll", "buildTransitive/x86/libSkiaSharp.dll", "microsoft.maui.resizetizer.8.0.91.nupkg.sha512", "microsoft.maui.resizetizer.nuspec"]}, "Microsoft.Windows.SDK.BuildTools/10.0.22621.756": {"sha512": "7ZL2sFSioYm1Ry067Kw1hg0SCcW5kuVezC2SwjGbcPE61Nn+gTbH86T73G3LcEOVj0S3IZzNuE/29gZvOLS7VA==", "type": "package", "path": "microsoft.windows.sdk.buildtools/10.0.22621.756", "files": [".nupkg.metadata", ".signature.p7s", "bin/10.0.22621.0/arm/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/arm/DeployUtil.exe", "bin/10.0.22621.0/arm64/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/arm64/ComparePackage.exe", "bin/10.0.22621.0/arm64/DeployUtil.exe", "bin/10.0.22621.0/arm64/MakeCert.exe", "bin/10.0.22621.0/arm64/Microsoft.ComparePackage.Lib.dll", "bin/10.0.22621.0/arm64/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/arm64/Microsoft.PackageEditor.Lib.dll", "bin/10.0.22621.0/arm64/Microsoft.Tools.Connectivity.dll", "bin/10.0.22621.0/arm64/Microsoft.Tools.Deploy.dll", "bin/10.0.22621.0/arm64/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.22621.0/arm64/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.22621.0/arm64/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.22621.0/arm64/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.22621.0/arm64/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.22621.0/arm64/PackageEditor.exe", "bin/10.0.22621.0/arm64/ServicingCommon.dll", "bin/10.0.22621.0/arm64/SirepClient.assembly.manifest", "bin/10.0.22621.0/arm64/SirepClient.dll", "bin/10.0.22621.0/arm64/SirepInterop.dll", "bin/10.0.22621.0/arm64/SshClient.dll", "bin/10.0.22621.0/arm64/WinAppDeployCmd.exe", "bin/10.0.22621.0/arm64/WinAppDeployCommon.dll", "bin/10.0.22621.0/arm64/appxpackaging.dll", "bin/10.0.22621.0/arm64/appxsip.dll", "bin/10.0.22621.0/arm64/en-US/AppxPackaging.dll.mui", "bin/10.0.22621.0/arm64/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.22621.0/arm64/en/WinAppDeployCmd.resources.dll", "bin/10.0.22621.0/arm64/ipoverusb.discoverpartners.dll", "bin/10.0.22621.0/arm64/makeappx.exe", "bin/10.0.22621.0/arm64/makecat.exe", "bin/10.0.22621.0/arm64/makecat.exe.manifest", "bin/10.0.22621.0/arm64/makepri.exe", "bin/10.0.22621.0/arm64/mc.exe", "bin/10.0.22621.0/arm64/mdmerge.exe", "bin/10.0.22621.0/arm64/midl.exe", "bin/10.0.22621.0/arm64/midlc.exe", "bin/10.0.22621.0/arm64/midlrt.exe", "bin/10.0.22621.0/arm64/midlrtmd.dll", "bin/10.0.22621.0/arm64/mrmsupport.dll", "bin/10.0.22621.0/arm64/msisip.dll", "bin/10.0.22621.0/arm64/mssign32.dll", "bin/10.0.22621.0/arm64/mt.exe", "bin/10.0.22621.0/arm64/mt.exe.config", "bin/10.0.22621.0/arm64/opcservices.dll", "bin/10.0.22621.0/arm64/rc.exe", "bin/10.0.22621.0/arm64/rcdll.dll", "bin/10.0.22621.0/arm64/signtool.exe", "bin/10.0.22621.0/arm64/signtool.exe.manifest", "bin/10.0.22621.0/arm64/tracewpp.exe", "bin/10.0.22621.0/arm64/uuidgen.exe", "bin/10.0.22621.0/arm64/winmdidl.exe", "bin/10.0.22621.0/arm64/wintrust.dll", "bin/10.0.22621.0/arm64/wintrust.dll.ini", "bin/10.0.22621.0/x64/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/x64/ComparePackage.exe", "bin/10.0.22621.0/x64/DeployUtil.exe", "bin/10.0.22621.0/x64/MakeCert.exe", "bin/10.0.22621.0/x64/Microsoft.ComparePackage.Lib.dll", "bin/10.0.22621.0/x64/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/x64/Microsoft.PackageEditor.Lib.dll", "bin/10.0.22621.0/x64/Microsoft.Tools.Connectivity.dll", "bin/10.0.22621.0/x64/Microsoft.Tools.Deploy.dll", "bin/10.0.22621.0/x64/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.22621.0/x64/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.22621.0/x64/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.22621.0/x64/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.22621.0/x64/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.22621.0/x64/PackageEditor.exe", "bin/10.0.22621.0/x64/ServicingCommon.dll", "bin/10.0.22621.0/x64/SirepClient.assembly.manifest", "bin/10.0.22621.0/x64/SirepClient.dll", "bin/10.0.22621.0/x64/SirepInterop.dll", "bin/10.0.22621.0/x64/SshClient.dll", "bin/10.0.22621.0/x64/WinAppDeployCmd.exe", "bin/10.0.22621.0/x64/WinAppDeployCommon.dll", "bin/10.0.22621.0/x64/appxpackaging.dll", "bin/10.0.22621.0/x64/appxsip.dll", "bin/10.0.22621.0/x64/en-US/AppxPackaging.dll.mui", "bin/10.0.22621.0/x64/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.22621.0/x64/en/WinAppDeployCmd.resources.dll", "bin/10.0.22621.0/x64/ipoverusb.discoverpartners.dll", "bin/10.0.22621.0/x64/makeappx.exe", "bin/10.0.22621.0/x64/makecat.exe", "bin/10.0.22621.0/x64/makecat.exe.manifest", "bin/10.0.22621.0/x64/makepri.exe", "bin/10.0.22621.0/x64/mc.exe", "bin/10.0.22621.0/x64/mdmerge.exe", "bin/10.0.22621.0/x64/midl.exe", "bin/10.0.22621.0/x64/midlc.exe", "bin/10.0.22621.0/x64/midlrt.exe", "bin/10.0.22621.0/x64/midlrtmd.dll", "bin/10.0.22621.0/x64/mrmsupport.dll", "bin/10.0.22621.0/x64/msisip.dll", "bin/10.0.22621.0/x64/mssign32.dll", "bin/10.0.22621.0/x64/mt.exe", "bin/10.0.22621.0/x64/mt.exe.config", "bin/10.0.22621.0/x64/opcservices.dll", "bin/10.0.22621.0/x64/rc.exe", "bin/10.0.22621.0/x64/rcdll.dll", "bin/10.0.22621.0/x64/signtool.exe", "bin/10.0.22621.0/x64/signtool.exe.manifest", "bin/10.0.22621.0/x64/tracewpp.exe", "bin/10.0.22621.0/x64/uuidgen.exe", "bin/10.0.22621.0/x64/winmdidl.exe", "bin/10.0.22621.0/x64/wintrust.dll", "bin/10.0.22621.0/x64/wintrust.dll.ini", "bin/10.0.22621.0/x86/AccChecker/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/x86/ComparePackage.exe", "bin/10.0.22621.0/x86/DeployUtil.exe", "bin/10.0.22621.0/x86/MakeCert.exe", "bin/10.0.22621.0/x86/Microsoft.ComparePackage.Lib.dll", "bin/10.0.22621.0/x86/Microsoft.Diagnostics.Tracing.EventSource.dll", "bin/10.0.22621.0/x86/Microsoft.PackageEditor.Lib.dll", "bin/10.0.22621.0/x86/Microsoft.Tools.Connectivity.dll", "bin/10.0.22621.0/x86/Microsoft.Tools.Deploy.dll", "bin/10.0.22621.0/x86/Microsoft.Windows.Build.Appx.AppxPackaging.dll.manifest", "bin/10.0.22621.0/x86/Microsoft.Windows.Build.Appx.AppxSip.dll.manifest", "bin/10.0.22621.0/x86/Microsoft.Windows.Build.Appx.OpcServices.dll.manifest", "bin/10.0.22621.0/x86/Microsoft.Windows.Build.Signing.mssign32.dll.manifest", "bin/10.0.22621.0/x86/Microsoft.Windows.Build.Signing.wintrust.dll.manifest", "bin/10.0.22621.0/x86/PackageEditor.exe", "bin/10.0.22621.0/x86/ServicingCommon.dll", "bin/10.0.22621.0/x86/SirepClient.assembly.manifest", "bin/10.0.22621.0/x86/SirepClient.dll", "bin/10.0.22621.0/x86/SirepInterop.dll", "bin/10.0.22621.0/x86/SshClient.dll", "bin/10.0.22621.0/x86/WinAppDeployCmd.exe", "bin/10.0.22621.0/x86/WinAppDeployCommon.dll", "bin/10.0.22621.0/x86/appxpackaging.dll", "bin/10.0.22621.0/x86/appxsip.dll", "bin/10.0.22621.0/x86/en-US/AppxPackaging.dll.mui", "bin/10.0.22621.0/x86/en/Microsoft.Tools.Deploy.resources.dll", "bin/10.0.22621.0/x86/en/WinAppDeployCmd.resources.dll", "bin/10.0.22621.0/x86/ipoverusb.discoverpartners.dll", "bin/10.0.22621.0/x86/makeappx.exe", "bin/10.0.22621.0/x86/makecat.exe", "bin/10.0.22621.0/x86/makecat.exe.manifest", "bin/10.0.22621.0/x86/makepri.exe", "bin/10.0.22621.0/x86/mc.exe", "bin/10.0.22621.0/x86/mdmerge.exe", "bin/10.0.22621.0/x86/midl.exe", "bin/10.0.22621.0/x86/midlc.exe", "bin/10.0.22621.0/x86/midlrt.exe", "bin/10.0.22621.0/x86/midlrtmd.dll", "bin/10.0.22621.0/x86/mrmsupport.dll", "bin/10.0.22621.0/x86/msisip.dll", "bin/10.0.22621.0/x86/mssign32.dll", "bin/10.0.22621.0/x86/mt.exe", "bin/10.0.22621.0/x86/mt.exe.config", "bin/10.0.22621.0/x86/opcservices.dll", "bin/10.0.22621.0/x86/rc.exe", "bin/10.0.22621.0/x86/rcdll.dll", "bin/10.0.22621.0/x86/signtool.exe", "bin/10.0.22621.0/x86/signtool.exe.manifest", "bin/10.0.22621.0/x86/tracewpp.exe", "bin/10.0.22621.0/x86/uuidgen.exe", "bin/10.0.22621.0/x86/winmdidl.exe", "bin/10.0.22621.0/x86/wintrust.dll", "bin/10.0.22621.0/x86/wintrust.dll.ini", "build/Microsoft.Windows.SDK.BuildTools.props", "build/Microsoft.Windows.SDK.BuildTools.targets", "buildTransitive/Microsoft.Windows.SDK.BuildTools.props", "buildTransitive/Microsoft.Windows.SDK.BuildTools.targets", "microsoft.windows.sdk.buildtools.10.0.22621.756.nupkg.sha512", "microsoft.windows.sdk.buildtools.nuspec", "schemas/10.0.22621.0/winrt/AppxManifestSchema.xsd", "schemas/10.0.22621.0/winrt/AppxManifestSchema2010_v2.xsd", "schemas/10.0.22621.0/winrt/AppxManifestSchema2013.xsd", "schemas/10.0.22621.0/winrt/FoundationManifestSchema.xsd", "schemas/10.0.22621.0/winrt/FoundationManifestSchema_v2.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v10.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v11.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v12.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v13.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v2.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v3.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v4.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v5.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v6.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v7.xsd", "schemas/10.0.22621.0/winrt/UapManifestSchema_v8.xsd"]}, "Microsoft.WindowsAppSDK/1.5.240802000": {"sha512": "XW6cNzbGKDuzn+iCY1L9pNDm3pF78te8PXjKWEYk+rgePVPV/j2otr75Q5NMbWzsMF461Qq7aPJZKUQAQJNISQ==", "type": "package", "path": "microsoft.windowsappsdk/1.5.240802000", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "NOTICE.txt", "WindowsAppSDK-VersionInfo.json", "WindowsAppSDK-VersionInfo.xml", "build/AppDevPackageScripts/Add-AppDevPackage.ps1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/cs-CZ/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/de-DE/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/en-US/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/es-ES/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/fr-FR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/it-IT/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ja-JP/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ko-KR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/pl-PL/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/pt-BR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/ru-RU/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/tr-TR/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/zh-CN/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Add-AppDevPackage.resources/zh-TW/Add-AppDevPackage.psd1", "build/AppDevPackageScripts/Install.ps1", "build/AppDevPackageScripts/LogSideloadingTelemetry.ps1", "build/Landing/extras/br.png", "build/Landing/extras/br_snippet.png", "build/Landing/image.png", "build/Landing/index.template.html", "build/Landing/logo.png", "build/Landing/style.css", "build/Microsoft.Build.Msix.Common.props", "build/Microsoft.Build.Msix.Cpp.props", "build/Microsoft.Build.Msix.Cpp.targets", "build/Microsoft.Build.Msix.Cs.targets", "build/Microsoft.Build.Msix.DesignTime.targets", "build/Microsoft.Build.Msix.Packaging.targets", "build/Microsoft.Build.Msix.Pri.targets", "build/Microsoft.Build.Msix.props", "build/Microsoft.Build.Msix.targets", "build/Microsoft.InteractiveExperiences.Capabilities.props", "build/Microsoft.InteractiveExperiences.Capabilities.targets", "build/Microsoft.InteractiveExperiences.Common.props", "build/Microsoft.InteractiveExperiences.Common.targets", "build/Microsoft.InteractiveExperiences.props", "build/Microsoft.InteractiveExperiences.targets", "build/Microsoft.UI.Xaml.Markup.Compiler.interop.targets", "build/Microsoft.UI.Xaml.Markup.Compiler.props", "build/Microsoft.UI.Xaml.Markup.Compiler.targets", "build/Microsoft.WinUI.AppX.targets", "build/Microsoft.WinUI.NET.Markup.Compiler.targets", "build/Microsoft.WinUI.ProjectCapabilities.props", "build/Microsoft.WinUI.References.targets", "build/Microsoft.WinUI.props", "build/Microsoft.WinUI.targets", "build/Microsoft.WindowsAppSDK.AppXReference.props", "build/Microsoft.WindowsAppSDK.Bootstrap.CS.targets", "build/Microsoft.WindowsAppSDK.BootstrapCommon.targets", "build/Microsoft.WindowsAppSDK.Common.props", "build/Microsoft.WindowsAppSDK.DWrite.ProjectCapabilities.props", "build/Microsoft.WindowsAppSDK.DWrite.props", "build/Microsoft.WindowsAppSDK.DWrite.targets", "build/Microsoft.WindowsAppSDK.DeploymentManager.CS.targets", "build/Microsoft.WindowsAppSDK.DeploymentManagerCommon.targets", "build/Microsoft.WindowsAppSDK.Foundation.props", "build/Microsoft.WindowsAppSDK.Foundation.targets", "build/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "build/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "build/Microsoft.WindowsAppSDK.Metapackage.props", "build/Microsoft.WindowsAppSDK.SelfContained.targets", "build/Microsoft.WindowsAppSDK.SingleFile.targets", "build/Microsoft.WindowsAppSDK.UndockedRegFreeWinRT.CS.targets", "build/Microsoft.WindowsAppSDK.UndockedRegFreeWinRTCommon.targets", "build/Microsoft.WindowsAppSDK.Widgets.targets", "build/Microsoft.WindowsAppSDK.WinUI.props", "build/Microsoft.WindowsAppSDK.WinUI.targets", "build/Microsoft.WindowsAppSDK.props", "build/Microsoft.WindowsAppSDK.targets", "build/Microsoft.Xaml.Tooling.targets", "build/MicrosoftWindowsAppSDKFoundationAppXVersion.props", "build/MrtCore.PriGen.targets", "build/MrtCore.References.targets", "build/MrtCore.props", "build/MrtCore.targets", "build/ProjectItemsSchema.xaml", "build/README.md", "build/Rules/MsixPackageDebugPropertyPage.xaml", "build/Rules/WindowsPackageTypePropertyPage.xaml", "build/Rules/af-ZA/MsixPackageDebugPropertyPage.xaml", "build/Rules/ar-SA/MsixPackageDebugPropertyPage.xaml", "build/Rules/az-Latn-AZ/MsixPackageDebugPropertyPage.xaml", "build/Rules/bg-BG/MsixPackageDebugPropertyPage.xaml", "build/Rules/bs-Latn-BA/MsixPackageDebugPropertyPage.xaml", "build/Rules/ca-ES/MsixPackageDebugPropertyPage.xaml", "build/Rules/cs-CZ/MsixPackageDebugPropertyPage.xaml", "build/Rules/cy-GB/MsixPackageDebugPropertyPage.xaml", "build/Rules/da-DK/MsixPackageDebugPropertyPage.xaml", "build/Rules/de-DE/MsixPackageDebugPropertyPage.xaml", "build/Rules/el-GR/MsixPackageDebugPropertyPage.xaml", "build/Rules/en-GB/MsixPackageDebugPropertyPage.xaml", "build/Rules/es-ES/MsixPackageDebugPropertyPage.xaml", "build/Rules/es-MX/MsixPackageDebugPropertyPage.xaml", "build/Rules/et-EE/MsixPackageDebugPropertyPage.xaml", "build/Rules/eu-ES/MsixPackageDebugPropertyPage.xaml", "build/Rules/fa-IR/MsixPackageDebugPropertyPage.xaml", "build/Rules/fi-FI/MsixPackageDebugPropertyPage.xaml", "build/Rules/fr-CA/MsixPackageDebugPropertyPage.xaml", "build/Rules/fr-FR/MsixPackageDebugPropertyPage.xaml", "build/Rules/gl-ES/MsixPackageDebugPropertyPage.xaml", "build/Rules/he-IL/MsixPackageDebugPropertyPage.xaml", "build/Rules/hi-IN/MsixPackageDebugPropertyPage.xaml", "build/Rules/hr-HR/MsixPackageDebugPropertyPage.xaml", "build/Rules/hu-HU/MsixPackageDebugPropertyPage.xaml", "build/Rules/id-ID/MsixPackageDebugPropertyPage.xaml", "build/Rules/is-IS/MsixPackageDebugPropertyPage.xaml", "build/Rules/it-IT/MsixPackageDebugPropertyPage.xaml", "build/Rules/ja-JP/MsixPackageDebugPropertyPage.xaml", "build/Rules/ka-GE/MsixPackageDebugPropertyPage.xaml", "build/Rules/kk-KZ/MsixPackageDebugPropertyPage.xaml", "build/Rules/ko-KR/MsixPackageDebugPropertyPage.xaml", "build/Rules/lt-LT/MsixPackageDebugPropertyPage.xaml", "build/Rules/lv-LV/MsixPackageDebugPropertyPage.xaml", "build/Rules/ms-MY/MsixPackageDebugPropertyPage.xaml", "build/Rules/nb-NO/MsixPackageDebugPropertyPage.xaml", "build/Rules/nl-NL/MsixPackageDebugPropertyPage.xaml", "build/Rules/nn-NO/MsixPackageDebugPropertyPage.xaml", "build/Rules/pl-PL/MsixPackageDebugPropertyPage.xaml", "build/Rules/pt-BR/MsixPackageDebugPropertyPage.xaml", "build/Rules/pt-PT/MsixPackageDebugPropertyPage.xaml", "build/Rules/ro-RO/MsixPackageDebugPropertyPage.xaml", "build/Rules/ru-RU/MsixPackageDebugPropertyPage.xaml", "build/Rules/sk-SK/MsixPackageDebugPropertyPage.xaml", "build/Rules/sl-SI/MsixPackageDebugPropertyPage.xaml", "build/Rules/sq-AL/MsixPackageDebugPropertyPage.xaml", "build/Rules/sr-Cyrl-RS/MsixPackageDebugPropertyPage.xaml", "build/Rules/sr-Latn-RS/MsixPackageDebugPropertyPage.xaml", "build/Rules/sv-SE/MsixPackageDebugPropertyPage.xaml", "build/Rules/th-TH/MsixPackageDebugPropertyPage.xaml", "build/Rules/tr-TR/MsixPackageDebugPropertyPage.xaml", "build/Rules/uk-UA/MsixPackageDebugPropertyPage.xaml", "build/Rules/vi-VN/MsixPackageDebugPropertyPage.xaml", "build/Rules/zh-CN/MsixPackageDebugPropertyPage.xaml", "build/Rules/zh-TW/MsixPackageDebugPropertyPage.xaml", "build/Templates/Package.appinstaller", "build/native/LiftedWinRTClassRegistrations.xml", "build/native/Microsoft.InteractiveExperiences.props", "build/native/Microsoft.InteractiveExperiences.targets", "build/native/Microsoft.WinUI.References.targets", "build/native/Microsoft.WinUI.props", "build/native/Microsoft.WinUI.targets", "build/native/Microsoft.WindowsAppSDK.Foundation.props", "build/native/Microsoft.WindowsAppSDK.Foundation.targets", "build/native/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "build/native/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "build/native/Microsoft.WindowsAppSDK.Widgets.targets", "build/native/Microsoft.WindowsAppSDK.WinUI.props", "build/native/Microsoft.WindowsAppSDK.WinUI.targets", "build/native/Microsoft.WindowsAppSDK.props", "build/native/Microsoft.WindowsAppSDK.targets", "build/native/MrtCore.C.props", "build/native/MrtCore.props", "build/native/MrtCore.targets", "build/native/WindowsAppSDK-Nuget-Native.Bootstrap.targets", "build/native/WindowsAppSDK-Nuget-Native.C.props", "build/native/WindowsAppSDK-Nuget-Native.DeploymentManager.targets", "build/native/WindowsAppSDK-Nuget-Native.UndockedRegFreeWinRT.targets", "build/native/WindowsAppSDK-Nuget-Native.WinRt.props", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.ps1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/cs-CZ/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/de-DE/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/en-US/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/es-ES/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/fr-FR/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/it-IT/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/ja-JP/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/ko-KR/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/pl-PL/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/pt-BR/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/ru-RU/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/tr-TR/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/zh-CN/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Add-AppDevPackage.resources/zh-TW/Add-AppDevPackage.psd1", "buildTransitive/AppDevPackageScripts/Install.ps1", "buildTransitive/AppDevPackageScripts/LogSideloadingTelemetry.ps1", "buildTransitive/Landing/extras/br.png", "buildTransitive/Landing/extras/br_snippet.png", "buildTransitive/Landing/image.png", "buildTransitive/Landing/index.template.html", "buildTransitive/Landing/logo.png", "buildTransitive/Landing/style.css", "buildTransitive/Microsoft.Build.Msix.Common.props", "buildTransitive/Microsoft.Build.Msix.Cpp.props", "buildTransitive/Microsoft.Build.Msix.Cpp.targets", "buildTransitive/Microsoft.Build.Msix.Cs.targets", "buildTransitive/Microsoft.Build.Msix.DesignTime.targets", "buildTransitive/Microsoft.Build.Msix.Packaging.targets", "buildTransitive/Microsoft.Build.Msix.Pri.targets", "buildTransitive/Microsoft.Build.Msix.props", "buildTransitive/Microsoft.Build.Msix.targets", "buildTransitive/Microsoft.InteractiveExperiences.Capabilities.props", "buildTransitive/Microsoft.InteractiveExperiences.Capabilities.targets", "buildTransitive/Microsoft.InteractiveExperiences.Common.props", "buildTransitive/Microsoft.InteractiveExperiences.Common.targets", "buildTransitive/Microsoft.InteractiveExperiences.props", "buildTransitive/Microsoft.InteractiveExperiences.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.interop.targets", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.props", "buildTransitive/Microsoft.UI.Xaml.Markup.Compiler.targets", "buildTransitive/Microsoft.WinUI.AppX.targets", "buildTransitive/Microsoft.WinUI.NET.Markup.Compiler.targets", "buildTransitive/Microsoft.WinUI.ProjectCapabilities.props", "buildTransitive/Microsoft.WinUI.References.targets", "buildTransitive/Microsoft.WinUI.props", "buildTransitive/Microsoft.WinUI.targets", "buildTransitive/Microsoft.WindowsAppSDK.AppXReference.props", "buildTransitive/Microsoft.WindowsAppSDK.Bootstrap.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.BootstrapCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Common.props", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.ProjectCapabilities.props", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.props", "buildTransitive/Microsoft.WindowsAppSDK.DWrite.targets", "buildTransitive/Microsoft.WindowsAppSDK.DeploymentManager.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.DeploymentManagerCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Foundation.props", "buildTransitive/Microsoft.WindowsAppSDK.Foundation.targets", "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "buildTransitive/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "buildTransitive/Microsoft.WindowsAppSDK.Metapackage.props", "buildTransitive/Microsoft.WindowsAppSDK.SelfContained.targets", "buildTransitive/Microsoft.WindowsAppSDK.SingleFile.targets", "buildTransitive/Microsoft.WindowsAppSDK.UndockedRegFreeWinRT.CS.targets", "buildTransitive/Microsoft.WindowsAppSDK.UndockedRegFreeWinRTCommon.targets", "buildTransitive/Microsoft.WindowsAppSDK.Widgets.targets", "buildTransitive/Microsoft.WindowsAppSDK.WinUI.props", "buildTransitive/Microsoft.WindowsAppSDK.WinUI.targets", "buildTransitive/Microsoft.WindowsAppSDK.props", "buildTransitive/Microsoft.WindowsAppSDK.targets", "buildTransitive/Microsoft.Xaml.Tooling.targets", "buildTransitive/MicrosoftWindowsAppSDKFoundationAppXVersion.props", "buildTransitive/MrtCore.PriGen.targets", "buildTransitive/MrtCore.References.targets", "buildTransitive/MrtCore.props", "buildTransitive/MrtCore.targets", "buildTransitive/ProjectItemsSchema.xaml", "buildTransitive/README.md", "buildTransitive/Rules/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/WindowsPackageTypePropertyPage.xaml", "buildTransitive/Rules/af-ZA/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/ar-SA/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/az-Latn-AZ/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/bg-BG/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/bs-Latn-BA/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/ca-ES/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/cs-CZ/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/cy-GB/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/da-DK/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/de-DE/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/el-GR/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/en-GB/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/es-ES/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/es-MX/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/et-EE/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/eu-ES/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/fa-IR/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/fi-FI/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/fr-CA/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/fr-FR/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/gl-ES/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/he-IL/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/hi-IN/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/hr-HR/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/hu-HU/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/id-ID/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/is-IS/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/it-IT/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/ja-JP/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/ka-GE/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/kk-KZ/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/ko-KR/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/lt-LT/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/lv-LV/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/ms-MY/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/nb-NO/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/nl-NL/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/nn-NO/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/pl-PL/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/pt-BR/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/pt-PT/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/ro-RO/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/ru-RU/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/sk-SK/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/sl-SI/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/sq-AL/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/sr-Cyrl-RS/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/sr-Latn-RS/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/sv-SE/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/th-TH/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/tr-TR/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/uk-UA/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/vi-VN/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/zh-CN/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Rules/zh-TW/MsixPackageDebugPropertyPage.xaml", "buildTransitive/Templates/Package.appinstaller", "buildTransitive/native/LiftedWinRTClassRegistrations.xml", "buildTransitive/native/Microsoft.InteractiveExperiences.props", "buildTransitive/native/Microsoft.InteractiveExperiences.targets", "buildTransitive/native/Microsoft.WinUI.References.targets", "buildTransitive/native/Microsoft.WinUI.props", "buildTransitive/native/Microsoft.WinUI.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.Foundation.props", "buildTransitive/native/Microsoft.WindowsAppSDK.Foundation.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.InteractiveExperiences.props", "buildTransitive/native/Microsoft.WindowsAppSDK.InteractiveExperiences.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.Widgets.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.WinUI.props", "buildTransitive/native/Microsoft.WindowsAppSDK.WinUI.targets", "buildTransitive/native/Microsoft.WindowsAppSDK.props", "buildTransitive/native/Microsoft.WindowsAppSDK.targets", "buildTransitive/native/MrtCore.C.props", "buildTransitive/native/MrtCore.props", "buildTransitive/native/MrtCore.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.Bootstrap.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.C.props", "buildTransitive/native/WindowsAppSDK-Nuget-Native.DeploymentManager.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.UndockedRegFreeWinRT.targets", "buildTransitive/native/WindowsAppSDK-Nuget-Native.WinRt.props", "include/DeploymentManagerAutoInitializer.cpp", "include/DeploymentManagerAutoInitializer.cs", "include/MRM.h", "include/MddBootstrap.h", "include/MddBootstrapAutoInitializer.cpp", "include/MddBootstrapAutoInitializer.cs", "include/Microsoft.UI.Composition.Interop.h", "include/Microsoft.UI.Dispatching.Interop.h", "include/Microsoft.UI.Input.InputCursor.Interop.h", "include/Microsoft.UI.Input.InputPreTranslateSource.Interop.h", "include/Microsoft.UI.Interop.h", "include/Microsoft.Windows.ApplicationModel.Resources.idl", "include/MsixDynamicDependency.h", "include/Security.AccessControl.h", "include/UndockedRegFreeWinRT-AutoInitializer.cpp", "include/UndockedRegFreeWinRT-AutoInitializer.cs", "include/WebView2.h", "include/WebView2.idl", "include/WindowsAppRuntimeInsights.h", "include/WindowsAppSDK-VersionInfo.cs", "include/WindowsAppSDK-VersionInfo.h", "include/dwrite.h", "include/dwrite_1.h", "include/dwrite_2.h", "include/dwrite_3.h", "include/dwrite_core.h", "include/microsoft.ui.xaml.hosting.referencetracker.h", "include/microsoft.ui.xaml.hosting.referencetracker.idl", "include/microsoft.ui.xaml.media.dxinterop.h", "include/microsoft.ui.xaml.media.dxinterop.idl", "include/microsoft.ui.xaml.window.h", "include/microsoft.ui.xaml.window.idl", "include/wil_msixdynamicdependency.h", "include/winrt/Microsoft.UI.Composition.Interop.h", "include/winrt/Microsoft.UI.Input.InputCursor.Interop.h", "include/winrt/Microsoft.UI.Input.InputPreTranslateSource.Interop.h", "include/winrt/Microsoft.UI.Interop.h", "include/winrtdirect3d11.h", "include/winrtdirectxcommon.h", "include/xamlom.winui.h", "include/xamlom.winui.idl", "lib/native/win10-arm64/Microsoft.UI.Dispatching.lib", "lib/native/win10-x64/Microsoft.UI.Dispatching.lib", "lib/native/win10-x86/Microsoft.UI.Dispatching.lib", "lib/net6.0-windows10.0.17763.0/Microsoft.InteractiveExperiences.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.InteractiveExperiences.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.WinUI/Themes/generic.xaml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppLifecycle.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.AppNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.Resources.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Management.Deployment.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.PushNotifications.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Security.AccessControl.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Power.Projection.xml", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.System.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.Windows.Widgets.Projection.dll", "lib/net6.0-windows10.0.17763.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.InteractiveExperiences.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.WinUI/Themes/generic.xaml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppLifecycle.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Builder.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.AppNotifications.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.Resources.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Management.Deployment.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.PushNotifications.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Security.AccessControl.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Power.Projection.xml", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.System.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.Windows.Widgets.Projection.dll", "lib/net6.0-windows10.0.18362.0/Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "lib/uap10.0.17763/Microsoft.Foundation.winmd", "lib/uap10.0.17763/Microsoft.Foundation.xml", "lib/uap10.0.17763/Microsoft.Graphics.winmd", "lib/uap10.0.17763/Microsoft.Graphics.xml", "lib/uap10.0.17763/Microsoft.UI.winmd", "lib/uap10.0.17763/Microsoft.UI.xml", "lib/uap10.0.18362/Microsoft.Foundation.winmd", "lib/uap10.0.18362/Microsoft.Foundation.xml", "lib/uap10.0.18362/Microsoft.Graphics.winmd", "lib/uap10.0.18362/Microsoft.Graphics.xml", "lib/uap10.0.18362/Microsoft.UI.winmd", "lib/uap10.0.18362/Microsoft.UI.xml", "lib/uap10.0/Microsoft.Foundation.xml", "lib/uap10.0/Microsoft.Graphics.xml", "lib/uap10.0/Microsoft.UI.Text.winmd", "lib/uap10.0/Microsoft.UI.Text.xml", "lib/uap10.0/Microsoft.UI.Xaml.winmd", "lib/uap10.0/Microsoft.UI.Xaml.xml", "lib/uap10.0/Microsoft.UI/Themes/generic.xaml", "lib/uap10.0/Microsoft.Web.WebView2.Core.winmd", "lib/uap10.0/Microsoft.Windows.AppLifecycle.winmd", "lib/uap10.0/Microsoft.Windows.AppLifecycle.xml", "lib/uap10.0/Microsoft.Windows.AppNotifications.Builder.winmd", "lib/uap10.0/Microsoft.Windows.AppNotifications.winmd", "lib/uap10.0/Microsoft.Windows.ApplicationModel.DynamicDependency.winmd", "lib/uap10.0/Microsoft.Windows.ApplicationModel.DynamicDependency.xml", "lib/uap10.0/Microsoft.Windows.ApplicationModel.Resources.winmd", "lib/uap10.0/Microsoft.Windows.ApplicationModel.Resources.xml", "lib/uap10.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.winmd", "lib/uap10.0/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.xml", "lib/uap10.0/Microsoft.Windows.Management.Deployment.winmd", "lib/uap10.0/Microsoft.Windows.PushNotifications.winmd", "lib/uap10.0/Microsoft.Windows.PushNotifications.xml", "lib/uap10.0/Microsoft.Windows.Security.AccessControl.winmd", "lib/uap10.0/Microsoft.Windows.System.Power.winmd", "lib/uap10.0/Microsoft.Windows.System.Power.xml", "lib/uap10.0/Microsoft.Windows.System.winmd", "lib/uap10.0/Microsoft.Windows.System.xml", "lib/uap10.0/Microsoft.Windows.Widgets.winmd", "lib/win10-arm64/DWriteCore.lib", "lib/win10-arm64/MRM.lib", "lib/win10-arm64/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/win10-arm64/Microsoft.WindowsAppRuntime.lib", "lib/win10-x64/DWriteCore.lib", "lib/win10-x64/MRM.lib", "lib/win10-x64/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/win10-x64/Microsoft.WindowsAppRuntime.lib", "lib/win10-x86/DWriteCore.lib", "lib/win10-x86/MRM.lib", "lib/win10-x86/Microsoft.WindowsAppRuntime.Bootstrap.lib", "lib/win10-x86/Microsoft.WindowsAppRuntime.lib", "license.txt", "manifests/Microsoft.InteractiveExperiences.manifest", "manifests/Microsoft.WindowsAppSdk.Foundation.manifest", "manifests/manifests/Microsoft.WindowsAppSdk.WinUI.manifest", "microsoft.windowsappsdk.1.5.240802000.nupkg.sha512", "microsoft.windowsappsdk.nuspec", "runtimes/win-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win10-arm64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win10-x64/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "runtimes/win10-x86/native/Microsoft.WindowsAppRuntime.Bootstrap.dll", "tools/MSIX/win10-arm64/MSIX.inventory", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.1.5.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.DDLM.1.5.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.Main.1.5.msix", "tools/MSIX/win10-arm64/Microsoft.WindowsAppRuntime.Singleton.1.5.msix", "tools/MSIX/win10-x64/MSIX.inventory", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.1.5.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.DDLM.1.5.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.Main.1.5.msix", "tools/MSIX/win10-x64/Microsoft.WindowsAppRuntime.Singleton.1.5.msix", "tools/MSIX/win10-x86/MSIX.inventory", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.1.5.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.DDLM.1.5.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.Main.1.5.msix", "tools/MSIX/win10-x86/Microsoft.WindowsAppRuntime.Singleton.1.5.msix", "tools/NOTICE.txt", "tools/arm64/GenXbf.dll", "tools/net472/Microsoft.Bcl.AsyncInterfaces.dll", "tools/net472/Microsoft.Build.Framework.dll", "tools/net472/Microsoft.Build.Msix.dll", "tools/net472/Microsoft.Build.Utilities.Core.dll", "tools/net472/Microsoft.Build.dll", "tools/net472/Microsoft.Cci.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.IO.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.MSBuildInterop.dll", "tools/net472/Microsoft.UI.Xaml.Markup.Compiler.dll", "tools/net472/Microsoft.VisualStudio.RemoteControl.dll", "tools/net472/Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "tools/net472/Microsoft.VisualStudio.Telemetry.dll", "tools/net472/Microsoft.VisualStudio.Utilities.Internal.dll", "tools/net472/Newtonsoft.Json.dll", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/System.Text.Encodings.Web.dll", "tools/net472/System.Text.Json.dll", "tools/net472/System.Threading.Tasks.Dataflow.dll", "tools/net472/System.Threading.Tasks.Extensions.dll", "tools/net472/XamlCompiler.exe", "tools/net472/XamlCompiler.exe.config", "tools/net472/af-ZA/Microsoft.Build.Msix.resources.dll", "tools/net472/ar-SA/Microsoft.Build.Msix.resources.dll", "tools/net472/az-Latn-AZ/Microsoft.Build.Msix.resources.dll", "tools/net472/bg-BG/Microsoft.Build.Msix.resources.dll", "tools/net472/bs-Latn-BA/Microsoft.Build.Msix.resources.dll", "tools/net472/ca-ES/Microsoft.Build.Msix.resources.dll", "tools/net472/cs-CZ/Microsoft.Build.Msix.resources.dll", "tools/net472/cy-GB/Microsoft.Build.Msix.resources.dll", "tools/net472/da-DK/Microsoft.Build.Msix.resources.dll", "tools/net472/de-DE/Microsoft.Build.Msix.resources.dll", "tools/net472/el-GR/Microsoft.Build.Msix.resources.dll", "tools/net472/en-GB/Microsoft.Build.Msix.resources.dll", "tools/net472/es-ES/Microsoft.Build.Msix.resources.dll", "tools/net472/es-MX/Microsoft.Build.Msix.resources.dll", "tools/net472/et-EE/Microsoft.Build.Msix.resources.dll", "tools/net472/eu-ES/Microsoft.Build.Msix.resources.dll", "tools/net472/fa-IR/Microsoft.Build.Msix.resources.dll", "tools/net472/fi-FI/Microsoft.Build.Msix.resources.dll", "tools/net472/fr-CA/Microsoft.Build.Msix.resources.dll", "tools/net472/fr-FR/Microsoft.Build.Msix.resources.dll", "tools/net472/gl-ES/Microsoft.Build.Msix.resources.dll", "tools/net472/he-IL/Microsoft.Build.Msix.resources.dll", "tools/net472/hi-IN/Microsoft.Build.Msix.resources.dll", "tools/net472/hr-HR/Microsoft.Build.Msix.resources.dll", "tools/net472/hu-HU/Microsoft.Build.Msix.resources.dll", "tools/net472/id-ID/Microsoft.Build.Msix.resources.dll", "tools/net472/is-IS/Microsoft.Build.Msix.resources.dll", "tools/net472/it-IT/Microsoft.Build.Msix.resources.dll", "tools/net472/ja-JP/Microsoft.Build.Msix.resources.dll", "tools/net472/ka-GE/Microsoft.Build.Msix.resources.dll", "tools/net472/kk-KZ/Microsoft.Build.Msix.resources.dll", "tools/net472/ko-KR/Microsoft.Build.Msix.resources.dll", "tools/net472/lt-LT/Microsoft.Build.Msix.resources.dll", "tools/net472/lv-LV/Microsoft.Build.Msix.resources.dll", "tools/net472/ms-MY/Microsoft.Build.Msix.resources.dll", "tools/net472/nb-NO/Microsoft.Build.Msix.resources.dll", "tools/net472/nl-NL/Microsoft.Build.Msix.resources.dll", "tools/net472/nn-NO/Microsoft.Build.Msix.resources.dll", "tools/net472/pl-PL/Microsoft.Build.Msix.resources.dll", "tools/net472/pt-BR/Microsoft.Build.Msix.resources.dll", "tools/net472/pt-PT/Microsoft.Build.Msix.resources.dll", "tools/net472/ro-RO/Microsoft.Build.Msix.resources.dll", "tools/net472/ru-RU/Microsoft.Build.Msix.resources.dll", "tools/net472/sk-SK/Microsoft.Build.Msix.resources.dll", "tools/net472/sl-SI/Microsoft.Build.Msix.resources.dll", "tools/net472/sq-AL/Microsoft.Build.Msix.resources.dll", "tools/net472/sr-Cyrl-RS/Microsoft.Build.Msix.resources.dll", "tools/net472/sr-Latn-RS/Microsoft.Build.Msix.resources.dll", "tools/net472/sv-SE/Microsoft.Build.Msix.resources.dll", "tools/net472/th-TH/Microsoft.Build.Msix.resources.dll", "tools/net472/tr-TR/Microsoft.Build.Msix.resources.dll", "tools/net472/uk-UA/Microsoft.Build.Msix.resources.dll", "tools/net472/vi-VN/Microsoft.Build.Msix.resources.dll", "tools/net472/zh-CN/Microsoft.Build.Msix.resources.dll", "tools/net472/zh-TW/Microsoft.Build.Msix.resources.dll", "tools/net5.0/Microsoft.Build.Msix.dll", "tools/net5.0/Microsoft.Cci.dll", "tools/net5.0/Microsoft.VisualStudio.RemoteControl.dll", "tools/net5.0/Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "tools/net5.0/Microsoft.VisualStudio.Telemetry.dll", "tools/net5.0/Microsoft.VisualStudio.Utilities.Internal.dll", "tools/net5.0/Newtonsoft.Json.dll", "tools/net5.0/af-ZA/Microsoft.Build.Msix.resources.dll", "tools/net5.0/ar-SA/Microsoft.Build.Msix.resources.dll", "tools/net5.0/az-Latn-AZ/Microsoft.Build.Msix.resources.dll", "tools/net5.0/bg-BG/Microsoft.Build.Msix.resources.dll", "tools/net5.0/bs-Latn-BA/Microsoft.Build.Msix.resources.dll", "tools/net5.0/ca-ES/Microsoft.Build.Msix.resources.dll", "tools/net5.0/cs-CZ/Microsoft.Build.Msix.resources.dll", "tools/net5.0/cy-GB/Microsoft.Build.Msix.resources.dll", "tools/net5.0/da-DK/Microsoft.Build.Msix.resources.dll", "tools/net5.0/de-DE/Microsoft.Build.Msix.resources.dll", "tools/net5.0/el-GR/Microsoft.Build.Msix.resources.dll", "tools/net5.0/en-GB/Microsoft.Build.Msix.resources.dll", "tools/net5.0/es-ES/Microsoft.Build.Msix.resources.dll", "tools/net5.0/es-MX/Microsoft.Build.Msix.resources.dll", "tools/net5.0/et-EE/Microsoft.Build.Msix.resources.dll", "tools/net5.0/eu-ES/Microsoft.Build.Msix.resources.dll", "tools/net5.0/fa-IR/Microsoft.Build.Msix.resources.dll", "tools/net5.0/fi-FI/Microsoft.Build.Msix.resources.dll", "tools/net5.0/fr-CA/Microsoft.Build.Msix.resources.dll", "tools/net5.0/fr-FR/Microsoft.Build.Msix.resources.dll", "tools/net5.0/gl-ES/Microsoft.Build.Msix.resources.dll", "tools/net5.0/he-IL/Microsoft.Build.Msix.resources.dll", "tools/net5.0/hi-IN/Microsoft.Build.Msix.resources.dll", "tools/net5.0/hr-HR/Microsoft.Build.Msix.resources.dll", "tools/net5.0/hu-HU/Microsoft.Build.Msix.resources.dll", "tools/net5.0/id-ID/Microsoft.Build.Msix.resources.dll", "tools/net5.0/is-IS/Microsoft.Build.Msix.resources.dll", "tools/net5.0/it-IT/Microsoft.Build.Msix.resources.dll", "tools/net5.0/ja-JP/Microsoft.Build.Msix.resources.dll", "tools/net5.0/ka-GE/Microsoft.Build.Msix.resources.dll", "tools/net5.0/kk-KZ/Microsoft.Build.Msix.resources.dll", "tools/net5.0/ko-KR/Microsoft.Build.Msix.resources.dll", "tools/net5.0/lt-LT/Microsoft.Build.Msix.resources.dll", "tools/net5.0/lv-LV/Microsoft.Build.Msix.resources.dll", "tools/net5.0/ms-MY/Microsoft.Build.Msix.resources.dll", "tools/net5.0/nb-NO/Microsoft.Build.Msix.resources.dll", "tools/net5.0/nl-NL/Microsoft.Build.Msix.resources.dll", "tools/net5.0/nn-NO/Microsoft.Build.Msix.resources.dll", "tools/net5.0/pl-PL/Microsoft.Build.Msix.resources.dll", "tools/net5.0/pt-BR/Microsoft.Build.Msix.resources.dll", "tools/net5.0/pt-PT/Microsoft.Build.Msix.resources.dll", "tools/net5.0/ro-RO/Microsoft.Build.Msix.resources.dll", "tools/net5.0/ru-RU/Microsoft.Build.Msix.resources.dll", "tools/net5.0/sk-SK/Microsoft.Build.Msix.resources.dll", "tools/net5.0/sl-SI/Microsoft.Build.Msix.resources.dll", "tools/net5.0/sq-AL/Microsoft.Build.Msix.resources.dll", "tools/net5.0/sr-Cyrl-RS/Microsoft.Build.Msix.resources.dll", "tools/net5.0/sr-Latn-RS/Microsoft.Build.Msix.resources.dll", "tools/net5.0/sv-SE/Microsoft.Build.Msix.resources.dll", "tools/net5.0/th-TH/Microsoft.Build.Msix.resources.dll", "tools/net5.0/tr-TR/Microsoft.Build.Msix.resources.dll", "tools/net5.0/uk-UA/Microsoft.Build.Msix.resources.dll", "tools/net5.0/vi-VN/Microsoft.Build.Msix.resources.dll", "tools/net5.0/zh-CN/Microsoft.Build.Msix.resources.dll", "tools/net5.0/zh-TW/Microsoft.Build.Msix.resources.dll", "tools/net6.0/Microsoft.Bcl.AsyncInterfaces.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.IO.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.MSBuildInterop.dll", "tools/net6.0/Microsoft.UI.Xaml.Markup.Compiler.dll", "tools/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "tools/net6.0/System.Text.Encodings.Web.dll", "tools/net6.0/System.Text.Json.dll", "tools/x64/GenXbf.dll", "tools/x86/GenXbf.dll"]}, "Mono.TextTemplating/2.2.1": {"sha512": "KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "type": "package", "path": "mono.texttemplating/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net472/Mono.TextTemplating.dll", "lib/netstandard2.0/Mono.TextTemplating.dll", "mono.texttemplating.2.2.1.nupkg.sha512", "mono.texttemplating.nuspec"]}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"sha512": "BmAf6XWt4TqtowmiWe4/5rRot6GerAeklmOPfviOvwLoF5WwgxcJHAxZtySuyW9r9w+HLILnm8VfJFLCUJYW8A==", "type": "package", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.6", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid90/SQLitePCLRaw.batteries_v2.dll", "lib/net461/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.xml", "lib/net6.0-ios14.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-ios14.2/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-tvos10.0/SQLitePCLRaw.batteries_v2.dll", "lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll", "lib/xamarinios10/SQLitePCLRaw.batteries_v2.dll", "sqlitepclraw.bundle_e_sqlite3.2.1.6.nupkg.sha512", "sqlitepclraw.bundle_e_sqlite3.nuspec"]}, "SQLitePCLRaw.core/2.1.6": {"sha512": "wO6v9GeMx9CUngAet8hbO7xdm+M42p1XeJq47ogyRoYSvNSp0NGLI+MgC0bhrMk9C17MTVFlLiN6ylyExLCc5w==", "type": "package", "path": "sqlitepclraw.core/2.1.6", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SQLitePCLRaw.core.dll", "sqlitepclraw.core.2.1.6.nupkg.sha512", "sqlitepclraw.core.nuspec"]}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"sha512": "2ObJJLkIUIxRpOUlZNGuD4rICpBnrBR5anjyfUFQep4hMOIeqW+XGQYzrNmHSVz5xSWZ3klSbh7sFR6UyDj68Q==", "type": "package", "path": "sqlitepclraw.lib.e_sqlite3/2.1.6", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/net461/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net6.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net7.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net8.0/SQLitePCLRaw.lib.e_sqlite3.targets", "lib/net461/_._", "lib/netstandard2.0/_._", "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net7.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a", "runtimes/linux-arm/native/libe_sqlite3.so", "runtimes/linux-arm64/native/libe_sqlite3.so", "runtimes/linux-armel/native/libe_sqlite3.so", "runtimes/linux-mips64/native/libe_sqlite3.so", "runtimes/linux-musl-arm/native/libe_sqlite3.so", "runtimes/linux-musl-arm64/native/libe_sqlite3.so", "runtimes/linux-musl-x64/native/libe_sqlite3.so", "runtimes/linux-ppc64le/native/libe_sqlite3.so", "runtimes/linux-s390x/native/libe_sqlite3.so", "runtimes/linux-x64/native/libe_sqlite3.so", "runtimes/linux-x86/native/libe_sqlite3.so", "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib", "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib", "runtimes/osx-arm64/native/libe_sqlite3.dylib", "runtimes/osx-x64/native/libe_sqlite3.dylib", "runtimes/win-arm/native/e_sqlite3.dll", "runtimes/win-arm64/native/e_sqlite3.dll", "runtimes/win-x64/native/e_sqlite3.dll", "runtimes/win-x86/native/e_sqlite3.dll", "runtimes/win10-arm/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-arm64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x86/nativeassets/uap10.0/e_sqlite3.dll", "sqlitepclraw.lib.e_sqlite3.2.1.6.nupkg.sha512", "sqlitepclraw.lib.e_sqlite3.nuspec"]}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"sha512": "PQ2Oq3yepLY4P7ll145P3xtx2bX8xF4PzaKPRpw9jZlKvfe4LE/saAV82inND9usn1XRpmxXk7Lal3MTI+6CNg==", "type": "package", "path": "sqlitepclraw.provider.e_sqlite3/2.1.6", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/netstandard2.0/SQLitePCLRaw.provider.e_sqlite3.dll", "sqlitepclraw.provider.e_sqlite3.2.1.6.nupkg.sha512", "sqlitepclraw.provider.e_sqlite3.nuspec"]}, "System.CodeDom/4.4.0": {"sha512": "2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "type": "package", "path": "system.codedom/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.dll", "ref/net461/System.CodeDom.dll", "ref/net461/System.CodeDom.xml", "ref/netstandard2.0/System.CodeDom.dll", "ref/netstandard2.0/System.CodeDom.xml", "system.codedom.4.4.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.Immutable/6.0.0": {"sha512": "l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "type": "package", "path": "system.collections.immutable/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Collections.Immutable.dll", "lib/net461/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.6.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition/6.0.0": {"sha512": "d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "type": "package", "path": "system.composition/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Composition.targets", "buildTransitive/netcoreapp3.1/_._", "system.composition.6.0.0.nupkg.sha512", "system.composition.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.AttributedModel/6.0.0": {"sha512": "WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==", "type": "package", "path": "system.composition.attributedmodel/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Composition.AttributedModel.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Composition.AttributedModel.dll", "lib/net461/System.Composition.AttributedModel.xml", "lib/net6.0/System.Composition.AttributedModel.dll", "lib/net6.0/System.Composition.AttributedModel.xml", "lib/netstandard2.0/System.Composition.AttributedModel.dll", "lib/netstandard2.0/System.Composition.AttributedModel.xml", "system.composition.attributedmodel.6.0.0.nupkg.sha512", "system.composition.attributedmodel.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Convention/6.0.0": {"sha512": "XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "type": "package", "path": "system.composition.convention/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Composition.Convention.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Composition.Convention.dll", "lib/net461/System.Composition.Convention.xml", "lib/net6.0/System.Composition.Convention.dll", "lib/net6.0/System.Composition.Convention.xml", "lib/netstandard2.0/System.Composition.Convention.dll", "lib/netstandard2.0/System.Composition.Convention.xml", "system.composition.convention.6.0.0.nupkg.sha512", "system.composition.convention.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Hosting/6.0.0": {"sha512": "w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "type": "package", "path": "system.composition.hosting/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Composition.Hosting.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Composition.Hosting.dll", "lib/net461/System.Composition.Hosting.xml", "lib/net6.0/System.Composition.Hosting.dll", "lib/net6.0/System.Composition.Hosting.xml", "lib/netstandard2.0/System.Composition.Hosting.dll", "lib/netstandard2.0/System.Composition.Hosting.xml", "system.composition.hosting.6.0.0.nupkg.sha512", "system.composition.hosting.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.Runtime/6.0.0": {"sha512": "qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==", "type": "package", "path": "system.composition.runtime/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Composition.Runtime.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Composition.Runtime.dll", "lib/net461/System.Composition.Runtime.xml", "lib/net6.0/System.Composition.Runtime.dll", "lib/net6.0/System.Composition.Runtime.xml", "lib/netstandard2.0/System.Composition.Runtime.dll", "lib/netstandard2.0/System.Composition.Runtime.xml", "system.composition.runtime.6.0.0.nupkg.sha512", "system.composition.runtime.nuspec", "useSharedDesignerContext.txt"]}, "System.Composition.TypedParts/6.0.0": {"sha512": "iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "type": "package", "path": "system.composition.typedparts/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Composition.TypedParts.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Composition.TypedParts.dll", "lib/net461/System.Composition.TypedParts.xml", "lib/net6.0/System.Composition.TypedParts.dll", "lib/net6.0/System.Composition.TypedParts.xml", "lib/netstandard2.0/System.Composition.TypedParts.dll", "lib/netstandard2.0/System.Composition.TypedParts.xml", "system.composition.typedparts.6.0.0.nupkg.sha512", "system.composition.typedparts.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Pipelines/6.0.3": {"sha512": "ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "type": "package", "path": "system.io.pipelines/6.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.IO.Pipelines.dll", "lib/net461/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/netcoreapp3.1/System.IO.Pipelines.dll", "lib/netcoreapp3.1/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.6.0.3.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.3": {"sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "type": "package", "path": "system.memory/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.3.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Metadata/6.0.1": {"sha512": "III/lNMSn0ZRBuM9m5Cgbiho5j81u0FAEagFX5ta2DKbljZ3T0IpD8j+BIiHQPeKqJppWS9bGEp6JnKnWKze0g==", "type": "package", "path": "system.reflection.metadata/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Reflection.Metadata.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Reflection.Metadata.dll", "lib/net461/System.Reflection.Metadata.xml", "lib/net6.0/System.Reflection.Metadata.dll", "lib/net6.0/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "system.reflection.metadata.6.0.1.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encoding.CodePages/6.0.0": {"sha512": "ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "type": "package", "path": "system.text.encoding.codepages/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "buildTransitive/netcoreapp3.1/_._", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.xml", "lib/net6.0/System.Text.Encoding.CodePages.dll", "lib/net6.0/System.Text.Encoding.CodePages.xml", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp3.1/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.6.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.4": {"sha512": "bAkhgDJ88XTsqczoxEMliSrpijKZHhbJQldhAmObj/RbrN3sU5dcokuXmWJWsdQAhiMJ9bTayWsL1C9fbbCRhw==", "type": "package", "path": "system.text.json/8.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.4.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Channels/6.0.0": {"sha512": "TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "type": "package", "path": "system.threading.channels/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Threading.Channels.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Threading.Channels.dll", "lib/net461/System.Threading.Channels.xml", "lib/net6.0/System.Threading.Channels.dll", "lib/net6.0/System.Threading.Channels.xml", "lib/netcoreapp3.1/System.Threading.Channels.dll", "lib/netcoreapp3.1/System.Threading.Channels.xml", "lib/netstandard2.0/System.Threading.Channels.dll", "lib/netstandard2.0/System.Threading.Channels.xml", "lib/netstandard2.1/System.Threading.Channels.dll", "lib/netstandard2.1/System.Threading.Channels.xml", "system.threading.channels.6.0.0.nupkg.sha512", "system.threading.channels.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows10.0.19041": ["CommunityToolkit.Mvvm >= 8.2.2", "Microsoft.EntityFrameworkCore.Sqlite >= 8.0.8", "Microsoft.EntityFrameworkCore.Tools >= 8.0.8", "Microsoft.Extensions.Logging.Debug >= 8.0.0", "Microsoft.Maui.Controls >= 8.0.91", "Microsoft.Maui.Controls.Compatibility >= 8.0.91"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\vscodeapps\\taskremindergem\\TaskMaster.csproj", "projectName": "TaskMaster", "projectPath": "G:\\vscodeapps\\taskremindergem\\TaskMaster.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\vscodeapps\\taskremindergem\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows10.0.19041.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows10.0.19041": {"targetAlias": "net8.0-windows10.0.19041.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows10.0.19041": {"targetAlias": "net8.0-windows10.0.19041.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[8.0.91, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[8.0.91, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.19041.56, 10.0.19041.56]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win10-x64": {"#import": []}}}}